{"name": "Untitled_Workflow", "description": "Untitled_Workflow", "workflow_data": {"nodes": [{"id": "start-node", "type": "WorkflowNode", "position": {"x": 100, "y": 100}, "data": {"label": "Start", "type": "component", "originalType": "StartNode", "definition": {"name": "StartNode", "display_name": "Start", "description": "The starting point for all workflows. Only nodes connected to this node will be executed.", "category": "Input/Output", "icon": "Play", "beta": false, "inputs": [], "outputs": [{"name": "flow", "display_name": "Flow", "output_type": "Any"}], "is_valid": true, "path": "components.io.start_node"}, "config": {"collected_parameters": {"AgenticAI-1749976462114_query": {"node_id": "AgenticAI-1749976462114", "node_name": "AI Agent Executor", "input_name": "query", "connected_to_start": true, "required": true, "input_type": "string", "options": null}}}}, "width": 208, "height": 122, "selected": false, "dragging": false}, {"id": "AgenticAI-1749976462114", "type": "WorkflowNode", "position": {"x": 360, "y": -60}, "data": {"label": "AI Agent Executor", "type": "agent", "originalType": "AgenticAI", "definition": {"name": "AgenticAI", "display_name": "AI Agent Executor", "description": "Executes an AI agent with tools and memory using AutoGen.", "category": "AI", "icon": "Bot", "beta": true, "requires_approval": false, "inputs": [{"name": "model_provider", "display_name": "Model Provider", "info": "The AI model provider to use.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "OpenAI", "options": ["OpenAI", "Azure OpenAI", "Anthropic", "<PERSON>", "Google", "Gemini", "<PERSON><PERSON><PERSON>", "Ollama", "Custom"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "base_url", "display_name": "Base URL", "info": "Base URL for the API (leave empty for default provider URL).", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "model_provider", "field_value": "Custom", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Azure OpenAI", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Ollama", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "api_key", "display_name": "API Key", "info": "API key for the model provider. Can be entered directly or referenced from secure storage.", "input_type": "credential", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR", "credential_type": "api_key", "use_credential_id": false, "credential_id": ""}, {"name": "model_name", "display_name": "Model", "info": "Select the model to use. The list includes models from OpenAI, Anthropic, Google, Mistral, and Ollama.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "gpt-4o", "options": ["gpt-4o", "gpt-4o-mini", "gpt-4-turbo", "gpt-4", "gpt-3.5-turbo", "claude-3-5-sonnet-20241022", "claude-3-5-haiku-20241022", "claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307", "claude-2.1", "claude-2.0", "gemini-1.5-pro", "gemini-1.5-flash", "gemini-pro", "gemini-pro-vision", "mistral-large-latest", "mistral-medium-latest", "mistral-small-latest", "open-mistral-7b", "open-mixtral-8x7b", "open-mixtral-8x22b", "llama3.2", "llama3.1", "llama3", "llama2", "mistral", "mixtral", "phi3", "gemma", "codellama", "qwen2"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "temperature", "display_name": "Temperature", "info": "Controls randomness: 0 is deterministic, higher values are more random.", "input_type": "float", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 0.7, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "description", "display_name": "Description", "info": "Description of the agent for UI display.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "execution_type", "display_name": "Execution Type", "info": "Determines if agent handles single response or multi-turn conversation.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "response", "options": ["response", "interactive"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "query", "display_name": "Query/Objective", "info": "The task, query, or objective for the agent to accomplish. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "system_message", "display_name": "System Message", "info": "System prompt/instructions for the agent. If empty, will use default based on query.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "termination_condition", "display_name": "Termination Condition", "info": "Defines when multi-turn conversations should end. Required for interactive execution type.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "execution_type", "field_value": "interactive", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "max_tokens", "display_name": "<PERSON>", "info": "Maximum response length in tokens.", "input_type": "int", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 1000, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_variables", "display_name": "Input Variables", "info": "Dictionary of variables to provide to the agent. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "workflow_components", "display_name": "Workflow Components (Tools)", "info": "Connect workflow components (including MCP marketplace components) to use as tools for the agent.", "input_type": "dynamic_handle", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "memory", "display_name": "Memory Object", "info": "Connect a memory object from another node.", "input_type": "handle", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "autogen_agent_type", "display_name": "AutoGen Agent Type", "info": "The type of AutoGen agent to create internally.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": "Assistant", "options": ["Assistant", "UserProxy", "CodeExecutor"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "final_answer", "display_name": "Final Answer", "output_type": "string", "semantic_type": null, "method": null}, {"name": "intermediate_steps", "display_name": "Intermediate Steps", "output_type": "list", "semantic_type": null, "method": null}, {"name": "updated_memory", "display_name": "Updated Memory", "output_type": "Memory", "semantic_type": null, "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.ai.agenticai", "interface_issues": []}, "config": {"id": "AgenticAI-1749976462114", "name": "AI Agent Executor", "model_provider": "OpenAI", "base_url": "", "api_key": "", "model_name": "gpt-4o", "temperature": 0.7, "description": "", "execution_type": "response", "query": "", "system_message": "", "termination_condition": "", "max_tokens": 1000, "input_variables": {}, "autogen_agent_type": "Assistant", "num_tool_handles": 1, "tool_connections": {"tool_1": [{"node_id": "MCP_Candidate_Interview_candidate_suitability-1749976479412", "node_type": "MCP_Candidate_Interview_candidate_suitability", "node_label": "Candidate Interview - candidate_suitability", "component_definition": {"name": "MCP_Candidate_Interview_candidate_suitability", "display_name": "Candidate Interview - candidate_suitability", "description": "Analyze candidate suitability based on job description and resume", "category": "MCP", "icon": "Cloud", "beta": true, "inputs": [{"name": "resume_details", "display_name": "Resume Details", "info": " candidate's resume", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "jd_details", "display_name": "Jd Details", "info": "job description", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "any"}], "is_valid": true, "path": "mcp.mcp_candidate_interview_candidate_suitability", "type": "MCP", "mcp_info": {"server_id": "24e34760-ac08-4117-be8c-372b7a6b9f31", "server_path": "", "tool_name": "candidate_suitability", "input_schema": {"properties": {"resume_details": {"description": " candidate's resume", "title": "Resume Details", "type": "string"}, "jd_details": {"description": "job description", "title": "Jd Details", "type": "string"}}, "required": ["resume_details", "jd_details"], "title": "CandidateSuitabilitySchema", "type": "object"}, "output_schema": null}}}, {"node_id": "MCP_<PERSON><PERSON>_Web_Search_and_Extraction_Server_tavily-crawl-1749976522030", "node_type": "MCP_<PERSON><PERSON>_Web_Search_and_Extraction_Server_tavily-crawl", "node_label": "Tavily Web Search and Extraction Server - tavily-crawl", "component_definition": {"name": "MCP_<PERSON><PERSON>_Web_Search_and_Extraction_Server_tavily-crawl", "display_name": "Tavily Web Search and Extraction Server - tavily-crawl", "description": "A powerful web crawler that initiates a structured web crawl starting from a specified base URL. The crawler expands from that point like a tree, following internal links across pages. You can control how deep and wide it goes, and guide it to focus on specific sections of the site.", "category": "MCP", "icon": "Cloud", "beta": true, "inputs": [{"name": "url", "display_name": "url", "info": "The root URL to begin the crawl", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "max_depth", "display_name": "max depth", "info": "Max depth of the crawl. Defines how far from the base URL the crawler can explore.", "input_type": "int", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 1, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "max_breadth", "display_name": "max breadth", "info": "Max number of links to follow per level of the tree (i.e., per page)", "input_type": "int", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 20, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "limit", "display_name": "limit", "info": "Total number of links the crawler will process before stopping", "input_type": "int", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 50, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "instructions", "display_name": "instructions", "info": "Natural language instructions for the crawler", "input_type": "string", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "select_paths", "display_name": "select paths", "info": "Regex patterns to select only URLs with specific path patterns (e.g., /docs/.*, /api/v1.*)", "input_type": "array", "input_types": null, "required": false, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": [], "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "select_domains", "display_name": "select domains", "info": "Regex patterns to select crawling to specific domains or subdomains (e.g., ^docs\\.example\\.com$)", "input_type": "array", "input_types": null, "required": false, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": [], "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "allow_external", "display_name": "allow external", "info": "Whether to allow following links that go to external domains", "input_type": "bool", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "categories", "display_name": "categories", "info": "Filter URLs using predefined categories like documentation, blog, api, etc", "input_type": "array", "input_types": null, "required": false, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": [], "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "extract_depth", "display_name": "extract depth", "info": "Advanced extraction retrieves more data, including tables and embedded content, with higher success but may increase latency", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "basic", "options": ["basic", "advanced"], "visibility_rules": null, "visibility_logic": "OR"}], "outputs": [{"name": "test", "display_name": "test", "output_type": "string"}], "is_valid": true, "path": "mcp.mcp_tavily_web_search_and_extraction_server_tavily-crawl", "type": "MCP", "mcp_info": {"server_id": "fe0ccd31-8e7c-47d8-a8d0-02ffe7fd34f4", "server_path": "", "tool_name": "tavily-crawl", "input_schema": {"type": "object", "properties": {"url": {"type": "string", "description": "The root URL to begin the crawl"}, "max_depth": {"type": "integer", "description": "Max depth of the crawl. Defines how far from the base URL the crawler can explore.", "default": 1, "minimum": 1}, "max_breadth": {"type": "integer", "description": "Max number of links to follow per level of the tree (i.e., per page)", "default": 20, "minimum": 1}, "limit": {"type": "integer", "description": "Total number of links the crawler will process before stopping", "default": 50, "minimum": 1}, "instructions": {"type": "string", "description": "Natural language instructions for the crawler"}, "select_paths": {"type": "array", "items": {"type": "string"}, "description": "Regex patterns to select only URLs with specific path patterns (e.g., /docs/.*, /api/v1.*)", "default": []}, "select_domains": {"type": "array", "items": {"type": "string"}, "description": "Regex patterns to select crawling to specific domains or subdomains (e.g., ^docs\\.example\\.com$)", "default": []}, "allow_external": {"type": "boolean", "description": "Whether to allow following links that go to external domains", "default": false}, "categories": {"type": "array", "items": {"type": "string", "enum": ["Careers", "Blog", "Documentation", "About", "Pricing", "Community", "Developers", "Contact", "Media"]}, "description": "Filter URLs using predefined categories like documentation, blog, api, etc", "default": []}, "extract_depth": {"type": "string", "enum": ["basic", "advanced"], "description": "Advanced extraction retrieves more data, including tables and embedded content, with higher success but may increase latency", "default": "basic"}}, "required": ["url"]}, "output_schema": {"properties": {"test": {"type": "string", "description": "abc", "title": "test"}}}}}}, {"node_id": "MCP_google-calendar-mcp_update_event-1749977346769", "node_type": "MCP_google-calendar-mcp_update_event", "node_label": "google-calendar-mcp - update_event", "component_definition": {"name": "MCP_google-calendar-mcp_update_event", "display_name": "google-calendar-mcp - update_event", "description": "Update an existing calendar event", "category": "MCP", "icon": "Cloud", "beta": true, "inputs": [{"name": "eventId", "display_name": "eventId", "info": "Event ID to update", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "summary", "display_name": "summary", "info": "New event title", "input_type": "string", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "location", "display_name": "location", "info": "New event location", "input_type": "string", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "description", "display_name": "description", "info": "New event description", "input_type": "string", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "start", "display_name": "start", "info": "New start time in ISO format", "input_type": "string", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "end", "display_name": "end", "info": "New end time in ISO format", "input_type": "string", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "attendees", "display_name": "attendees", "info": "New list of attendee email addresses", "input_type": "array", "input_types": null, "required": false, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "any"}], "is_valid": true, "path": "mcp.mcp_google-calendar-mcp_update_event", "type": "MCP", "mcp_info": {"server_id": "c1a81b18-a294-477a-b69d-0120529b3964", "server_path": "", "tool_name": "update_event", "input_schema": {"type": "object", "properties": {"eventId": {"type": "string", "description": "Event ID to update"}, "summary": {"type": "string", "description": "New event title"}, "location": {"type": "string", "description": "New event location"}, "description": {"type": "string", "description": "New event description"}, "start": {"type": "string", "description": "New start time in ISO format"}, "end": {"type": "string", "description": "New end time in ISO format"}, "attendees": {"type": "array", "items": {"type": "string"}, "description": "New list of attendee email addresses"}}, "required": ["eventId"]}, "output_schema": null}}}, {"node_id": "MCP_mcp-fetch_fetch-1749977364402", "node_type": "MCP_mcp-fetch_fetch", "node_label": "mcp-fetch - fetch", "component_definition": {"name": "MCP_mcp-fetch_fetch", "display_name": "mcp-fetch - fetch", "description": "Retrieves URLs from the Internet and extracts their content as markdown. If images are found, their URLs will be included in the response.", "category": "MCP", "icon": "Cloud", "beta": true, "inputs": [{"name": "url", "display_name": "url", "info": "", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "max<PERSON><PERSON><PERSON>", "display_name": "max<PERSON><PERSON><PERSON>", "info": "", "input_type": "number", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 20000, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "startIndex", "display_name": "startIndex", "info": "", "input_type": "number", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "raw", "display_name": "raw", "info": "", "input_type": "bool", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "any"}], "is_valid": true, "path": "mcp.mcp_mcp-fetch_fetch", "type": "MCP", "mcp_info": {"server_id": "d00cfd0e-67ec-4dbd-9a9b-fcec170fd2b4", "server_path": "", "tool_name": "fetch", "input_schema": {"type": "object", "properties": {"url": {"type": "string", "format": "uri"}, "maxLength": {"type": "number", "exclusiveMinimum": 0, "maximum": 1000000, "default": 20000}, "startIndex": {"type": "number", "minimum": 0, "default": 0}, "raw": {"type": "boolean", "default": false}}, "required": ["url"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null}}}]}}}, "width": 208, "height": 218, "selected": false, "dragging": false, "style": {"opacity": 1}}], "edges": [{"id": "reactflow__edge-start-nodeflow-AgenticAI-1749976462114query", "source": "start-node", "sourceHandle": "flow", "target": "AgenticAI-1749976462114", "targetHandle": "query", "type": "default", "animated": true, "selected": false}]}, "start_node_data": [{"field": "query", "type": "string", "transition_id": "transition-AgenticAI-1749976462114"}]}