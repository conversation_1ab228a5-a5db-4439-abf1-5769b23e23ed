"""
Tests for agent platform tool consumption functionality
"""

import pytest
import asyncio
import json
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from typing import Dict, Any, List

from app.services.agent_executor import AgentExecutor
from app.utils.tool_function_generator import ToolFunctionGenerator
from app.models.agent_tools import Agent<PERSON><PERSON>, ToolSchema, MCPTool


class TestAgentExecutor:
    """Test agent executor tool consumption functionality"""

    def test_agent_executor_initialization(self):
        """Test that agent executor initializes correctly"""
        executor = AgentExecutor()
        
        assert executor.tool_function_generator is not None
        assert executor.active_agents == {}
        assert executor.tool_registry == {}

    @pytest.mark.asyncio
    async def test_consume_tool_schemas_from_kafka(self):
        """Test consuming tool schemas from Kafka messages"""
        executor = AgentExecutor()
        
        # Mock Kafka message with tool schemas
        kafka_message = {
            "agent_config": {
                "id": "test-agent-1",
                "name": "Test Agent",
                "tools": [
                    {
                        "tool_type": "workflow_component",
                        "component": {
                            "component_id": "data-processor-1",
                            "component_type": "DataProcessor",
                            "component_name": "Data Processing Tool",
                            "component_schema": {
                                "name": "data_processing_tool",
                                "description": "Process data efficiently",
                                "parameters": {
                                    "type": "object",
                                    "properties": {
                                        "input_data": {"type": "string"}
                                    },
                                    "required": ["input_data"],
                                    "additionalProperties": False
                                },
                                "strict": False
                            }
                        }
                    }
                ]
            },
            "request_id": "test-request-123"
        }
        
        # Process the Kafka message
        result = await executor.process_kafka_message(kafka_message)
        
        assert result["status"] == "success"
        assert "test-agent-1" in executor.active_agents
        assert len(executor.tool_registry["test-agent-1"]) == 1
        
        # Verify tool function was created
        tool_functions = executor.get_agent_tool_functions("test-agent-1")
        assert len(tool_functions) == 1
        assert tool_functions[0].name == "data_processing_tool"

    @pytest.mark.asyncio
    async def test_mcp_tool_consumption(self):
        """Test consuming MCP tool schemas from Kafka messages"""
        executor = AgentExecutor()
        
        # Mock Kafka message with MCP tool
        kafka_message = {
            "agent_config": {
                "id": "mcp-agent-1",
                "name": "MCP Agent",
                "tools": [
                    {
                        "tool_type": "mcp_marketplace",
                        "component": {
                            "component_id": "mcp-translate-1",
                            "component_type": "MCPMarketplace",
                            "component_name": "Translation Tool",
                            "component_schema": {
                                "name": "translate_text",
                                "description": "Translate text between languages",
                                "parameters": {
                                    "type": "object",
                                    "properties": {
                                        "text": {"type": "string"},
                                        "target_language": {"type": "string"}
                                    },
                                    "required": ["text", "target_language"],
                                    "additionalProperties": False
                                },
                                "strict": False
                            },
                            "mcp_metadata": {
                                "server_url": "http://localhost:8000",
                                "tool_name": "translate"
                            }
                        }
                    }
                ]
            },
            "request_id": "mcp-test-123"
        }
        
        # Process the MCP message
        result = await executor.process_kafka_message(kafka_message)
        
        assert result["status"] == "success"
        assert "mcp-agent-1" in executor.active_agents
        
        # Verify MCP tool function was created
        tool_functions = executor.get_agent_tool_functions("mcp-agent-1")
        assert len(tool_functions) == 1
        assert tool_functions[0].name == "translate_text"
        assert isinstance(tool_functions[0], MCPTool)

    @pytest.mark.asyncio
    async def test_invalid_schema_handling(self):
        """Test error handling for invalid tool schemas"""
        executor = AgentExecutor()
        
        # Mock Kafka message with invalid tool schema
        kafka_message = {
            "agent_config": {
                "id": "invalid-agent-1",
                "name": "Invalid Agent",
                "tools": [
                    {
                        "tool_type": "workflow_component",
                        "component": {
                            "component_id": "invalid-component",
                            # Missing required fields
                            "component_schema": {
                                "name": "invalid_tool"
                                # Missing description and parameters
                            }
                        }
                    }
                ]
            },
            "request_id": "invalid-test-123"
        }
        
        # Process the invalid message
        result = await executor.process_kafka_message(kafka_message)
        
        assert result["status"] == "partial_success"
        assert "invalid-agent-1" in executor.active_agents
        assert len(executor.tool_registry["invalid-agent-1"]) == 0  # No valid tools

    def test_tool_function_generation_performance(self):
        """Test that tool function generation meets performance requirements"""
        import time
        
        generator = ToolFunctionGenerator()
        
        # Create multiple tool schemas
        tool_schemas = []
        for i in range(10):
            tool_schemas.append({
                "tool_type": "workflow_component",
                "component": {
                    "component_id": f"component-{i}",
                    "component_type": f"Component{i}",
                    "component_name": f"Component {i}",
                    "component_schema": {
                        "name": f"tool_{i}",
                        "description": f"Tool {i} description",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "input": {"type": "string"}
                            },
                            "required": ["input"],
                            "additionalProperties": False
                        },
                        "strict": False
                    }
                }
            })
        
        # Measure generation time
        start_time = time.time()
        tool_functions = generator.generate_tool_functions(tool_schemas)
        end_time = time.time()
        
        generation_time = (end_time - start_time) * 1000  # Convert to milliseconds
        
        # Should generate in less than 100ms (performance requirement)
        assert generation_time < 100, f"Tool function generation took {generation_time:.2f}ms, should be < 100ms"
        
        # Verify all tools were generated
        assert len(tool_functions) == 10

    @pytest.mark.asyncio
    async def test_agent_tool_function_execution(self):
        """Test executing agent tool functions"""
        executor = AgentExecutor()
        
        # Mock tool function
        mock_tool_function = Mock()
        mock_tool_function.name = "test_tool"
        mock_tool_function.execute = AsyncMock(return_value={"status": "success", "result": "tool executed"})
        
        # Register tool function
        executor.tool_registry["test-agent"] = [mock_tool_function]
        
        # Execute tool function
        result = await executor.execute_agent_tool("test-agent", "test_tool", {"input": "test data"})
        
        assert result["status"] == "success"
        assert result["result"] == "tool executed"
        
        # Verify tool function was called
        mock_tool_function.execute.assert_called_once_with({"input": "test data"})

    def test_agent_registry_management(self):
        """Test agent registry management"""
        executor = AgentExecutor()
        
        # Register multiple agents
        agent_configs = [
            {"id": "agent-1", "name": "Agent 1", "tools": []},
            {"id": "agent-2", "name": "Agent 2", "tools": []},
            {"id": "agent-3", "name": "Agent 3", "tools": []}
        ]
        
        for config in agent_configs:
            executor.register_agent(config)
        
        # Verify all agents are registered
        assert len(executor.active_agents) == 3
        assert "agent-1" in executor.active_agents
        assert "agent-2" in executor.active_agents
        assert "agent-3" in executor.active_agents
        
        # Test unregistering an agent
        executor.unregister_agent("agent-2")
        assert len(executor.active_agents) == 2
        assert "agent-2" not in executor.active_agents


class TestToolFunctionGenerator:
    """Test tool function generator functionality"""

    def test_tool_function_generator_initialization(self):
        """Test tool function generator initialization"""
        generator = ToolFunctionGenerator()
        
        assert generator.supported_tool_types == ["workflow_component", "mcp_marketplace"]

    def test_generate_workflow_component_tool(self):
        """Test generating tool function from workflow component schema"""
        generator = ToolFunctionGenerator()
        
        tool_schema = {
            "tool_type": "workflow_component",
            "component": {
                "component_id": "api-caller-1",
                "component_type": "APICaller",
                "component_name": "API Calling Tool",
                "component_schema": {
                    "name": "call_api",
                    "description": "Call external API",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "url": {"type": "string"},
                            "method": {"type": "string", "enum": ["GET", "POST"]}
                        },
                        "required": ["url"],
                        "additionalProperties": False
                    },
                    "strict": False
                }
            }
        }
        
        # Generate tool function
        tool_function = generator.generate_single_tool_function(tool_schema)
        
        assert tool_function is not None
        assert tool_function.name == "call_api"
        assert tool_function.description == "Call external API"
        assert isinstance(tool_function, AgentTool)

    def test_generate_mcp_tool(self):
        """Test generating tool function from MCP schema"""
        generator = ToolFunctionGenerator()
        
        mcp_schema = {
            "tool_type": "mcp_marketplace",
            "component": {
                "component_id": "mcp-weather-1",
                "component_type": "MCPMarketplace",
                "component_name": "Weather Tool",
                "component_schema": {
                    "name": "get_weather",
                    "description": "Get weather information",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "location": {"type": "string"},
                            "units": {"type": "string", "enum": ["celsius", "fahrenheit"]}
                        },
                        "required": ["location"],
                        "additionalProperties": False
                    },
                    "strict": False
                },
                "mcp_metadata": {
                    "server_url": "http://weather.example.com",
                    "tool_name": "weather"
                }
            }
        }
        
        # Generate MCP tool function
        tool_function = generator.generate_single_tool_function(mcp_schema)
        
        assert tool_function is not None
        assert tool_function.name == "get_weather"
        assert isinstance(tool_function, MCPTool)
        assert tool_function.mcp_metadata["server_url"] == "http://weather.example.com"

    def test_batch_tool_generation(self):
        """Test batch generation of multiple tool functions"""
        generator = ToolFunctionGenerator()
        
        tool_schemas = [
            {
                "tool_type": "workflow_component",
                "component": {
                    "component_id": "tool-1",
                    "component_type": "Tool1",
                    "component_name": "Tool 1",
                    "component_schema": {
                        "name": "tool_1",
                        "description": "First tool",
                        "parameters": {
                            "type": "object",
                            "properties": {"input": {"type": "string"}},
                            "required": ["input"],
                            "additionalProperties": False
                        },
                        "strict": False
                    }
                }
            },
            {
                "tool_type": "mcp_marketplace",
                "component": {
                    "component_id": "mcp-tool-1",
                    "component_type": "MCPMarketplace",
                    "component_name": "MCP Tool 1",
                    "component_schema": {
                        "name": "mcp_tool_1",
                        "description": "First MCP tool",
                        "parameters": {
                            "type": "object",
                            "properties": {"data": {"type": "string"}},
                            "required": ["data"],
                            "additionalProperties": False
                        },
                        "strict": False
                    },
                    "mcp_metadata": {"server_url": "http://localhost:8000"}
                }
            }
        ]
        
        # Generate all tool functions
        tool_functions = generator.generate_tool_functions(tool_schemas)
        
        assert len(tool_functions) == 2
        assert tool_functions[0].name == "tool_1"
        assert tool_functions[1].name == "mcp_tool_1"
        assert isinstance(tool_functions[0], AgentTool)
        assert isinstance(tool_functions[1], MCPTool)

    def test_invalid_schema_handling(self):
        """Test handling of invalid tool schemas"""
        generator = ToolFunctionGenerator()
        
        invalid_schemas = [
            {
                "tool_type": "unsupported_type",
                "component": {"component_id": "invalid-1"}
            },
            {
                "tool_type": "workflow_component",
                "component": {
                    "component_id": "invalid-2",
                    # Missing required component_schema
                }
            }
        ]
        
        # Generate tool functions (should skip invalid ones)
        tool_functions = generator.generate_tool_functions(invalid_schemas)
        
        assert len(tool_functions) == 0  # No valid tools generated

    def test_tool_schema_validation(self):
        """Test tool schema validation"""
        generator = ToolFunctionGenerator()
        
        # Valid schema
        valid_schema = {
            "tool_type": "workflow_component",
            "component": {
                "component_id": "valid-tool",
                "component_type": "ValidTool",
                "component_name": "Valid Tool",
                "component_schema": {
                    "name": "valid_tool",
                    "description": "A valid tool",
                    "parameters": {
                        "type": "object",
                        "properties": {"input": {"type": "string"}},
                        "required": ["input"],
                        "additionalProperties": False
                    },
                    "strict": False
                }
            }
        }
        
        # Invalid schema (missing name)
        invalid_schema = {
            "tool_type": "workflow_component",
            "component": {
                "component_id": "invalid-tool",
                "component_schema": {
                    "description": "Missing name",
                    "parameters": {"type": "object"}
                }
            }
        }
        
        assert generator.validate_tool_schema(valid_schema) == True
        assert generator.validate_tool_schema(invalid_schema) == False


class TestAgentToolModels:
    """Test agent tool model functionality"""

    def test_agent_tool_creation(self):
        """Test creating AgentTool instances"""
        tool_schema = ToolSchema(
            name="test_tool",
            description="Test tool description",
            parameters={
                "type": "object",
                "properties": {"input": {"type": "string"}},
                "required": ["input"]
            }
        )
        
        agent_tool = AgentTool(
            name="test_tool",
            description="Test tool description",
            schema=tool_schema,
            component_id="test-component-1",
            component_type="TestComponent"
        )
        
        assert agent_tool.name == "test_tool"
        assert agent_tool.description == "Test tool description"
        assert agent_tool.component_id == "test-component-1"
        assert agent_tool.component_type == "TestComponent"

    def test_mcp_tool_creation(self):
        """Test creating MCPTool instances"""
        tool_schema = ToolSchema(
            name="mcp_test_tool",
            description="MCP test tool description",
            parameters={
                "type": "object",
                "properties": {"data": {"type": "string"}},
                "required": ["data"]
            }
        )
        
        mcp_tool = MCPTool(
            name="mcp_test_tool",
            description="MCP test tool description",
            schema=tool_schema,
            component_id="mcp-component-1",
            component_type="MCPMarketplace",
            mcp_metadata={
                "server_url": "http://localhost:8000",
                "tool_name": "test_tool"
            }
        )
        
        assert mcp_tool.name == "mcp_test_tool"
        assert isinstance(mcp_tool, MCPTool)
        assert mcp_tool.mcp_metadata["server_url"] == "http://localhost:8000"

    @pytest.mark.asyncio
    async def test_agent_tool_execution(self):
        """Test agent tool execution"""
        tool_schema = ToolSchema(
            name="executable_tool",
            description="Executable tool",
            parameters={
                "type": "object",
                "properties": {"input": {"type": "string"}},
                "required": ["input"]
            }
        )
        
        agent_tool = AgentTool(
            name="executable_tool",
            description="Executable tool",
            schema=tool_schema,
            component_id="exec-component-1",
            component_type="ExecutableComponent"
        )
        
        # Mock the execution
        with patch.object(agent_tool, '_execute_component', new_callable=AsyncMock) as mock_execute:
            mock_execute.return_value = {"status": "success", "result": "executed"}
            
            result = await agent_tool.execute({"input": "test data"})
            
            assert result["status"] == "success"
            assert result["result"]["status"] == "success"
            assert result["result"]["result"] == "executed"
            mock_execute.assert_called_once_with({"input": "test data"})
