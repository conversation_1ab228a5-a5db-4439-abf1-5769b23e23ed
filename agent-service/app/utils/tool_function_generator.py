"""
Tool Function Generator - Generates agent tool functions from component schemas
"""

import logging
import time
from typing import Dict, Any, List, Optional, Union

from app.models.agent_tools import AgentTool, MCPTool, ToolSchema

logger = logging.getLogger(__name__)


class ToolFunctionGenerator:
    """
    Generator for creating agent tool functions from component schemas.
    
    This class converts workflow component schemas and MCP marketplace schemas
    into executable agent tool functions that can be used by agents.
    """
    
    def __init__(self):
        """Initialize the tool function generator."""
        self.supported_tool_types = ["workflow_component", "mcp_marketplace"]
        self.generation_stats = {
            "total_generated": 0,
            "successful_generations": 0,
            "failed_generations": 0,
            "average_generation_time": 0.0
        }
        
        logger.info("ToolFunctionGenerator initialized")
    
    def generate_tool_functions(self, tool_schemas: List[Dict[str, Any]]) -> List[Union[AgentTool, MCPTool]]:
        """
        Generate multiple tool functions from a list of tool schemas.
        
        Args:
            tool_schemas: List of tool schema dictionaries
            
        Returns:
            List of generated tool function objects
        """
        start_time = time.time()
        tool_functions = []
        
        logger.info(f"Generating tool functions for {len(tool_schemas)} schemas")
        
        for schema in tool_schemas:
            try:
                tool_function = self.generate_single_tool_function(schema)
                if tool_function:
                    tool_functions.append(tool_function)
                    self.generation_stats["successful_generations"] += 1
                else:
                    self.generation_stats["failed_generations"] += 1
                    
            except Exception as e:
                logger.error(f"Error generating tool function: {str(e)}")
                self.generation_stats["failed_generations"] += 1
        
        # Update statistics
        generation_time = time.time() - start_time
        self.generation_stats["total_generated"] += len(tool_schemas)
        
        # Update average generation time
        total = self.generation_stats["total_generated"]
        current_avg = self.generation_stats["average_generation_time"]
        self.generation_stats["average_generation_time"] = (
            (current_avg * (total - len(tool_schemas)) + generation_time) / total
        )
        
        logger.info(f"Generated {len(tool_functions)} tool functions in {generation_time:.3f}s")
        return tool_functions
    
    def generate_single_tool_function(self, tool_schema: Dict[str, Any]) -> Optional[Union[AgentTool, MCPTool]]:
        """
        Generate a single tool function from a tool schema.
        
        Args:
            tool_schema: Tool schema dictionary
            
        Returns:
            Generated tool function object or None if generation failed
        """
        try:
            # Validate the tool schema
            if not self.validate_tool_schema(tool_schema):
                logger.error("Invalid tool schema provided")
                return None
            
            tool_type = tool_schema.get("tool_type")
            component = tool_schema.get("component", {})
            
            # Extract component information
            component_id = component.get("component_id")
            component_type = component.get("component_type")
            component_name = component.get("component_name", component_type)
            component_schema = component.get("component_schema", {})
            
            # Create ToolSchema object
            tool_schema_obj = ToolSchema(
                name=component_schema.get("name"),
                description=component_schema.get("description"),
                parameters=component_schema.get("parameters", {}),
                strict=component_schema.get("strict", False)
            )
            
            # Validate the tool schema object
            if not tool_schema_obj.validate():
                logger.error(f"Invalid tool schema for component {component_id}")
                return None
            
            # Generate appropriate tool function based on type
            if tool_type == "workflow_component":
                return self._generate_workflow_component_tool(
                    tool_schema_obj, component_id, component_type, component_name
                )
            elif tool_type == "mcp_marketplace":
                mcp_metadata = component.get("mcp_metadata", {})
                return self._generate_mcp_tool(
                    tool_schema_obj, component_id, component_type, component_name, mcp_metadata
                )
            else:
                logger.error(f"Unsupported tool type: {tool_type}")
                return None
                
        except Exception as e:
            logger.error(f"Error generating tool function: {str(e)}")
            return None
    
    def _generate_workflow_component_tool(
        self,
        tool_schema: ToolSchema,
        component_id: str,
        component_type: str,
        component_name: str
    ) -> AgentTool:
        """
        Generate a workflow component tool function.
        
        Args:
            tool_schema: Tool schema object
            component_id: Component identifier
            component_type: Component type
            component_name: Component name
            
        Returns:
            AgentTool instance
        """
        logger.debug(f"Generating workflow component tool: {tool_schema.name}")
        
        return AgentTool(
            name=tool_schema.name,
            description=tool_schema.description,
            schema=tool_schema,
            component_id=component_id,
            component_type=component_type
        )
    
    def _generate_mcp_tool(
        self,
        tool_schema: ToolSchema,
        component_id: str,
        component_type: str,
        component_name: str,
        mcp_metadata: Dict[str, Any]
    ) -> MCPTool:
        """
        Generate an MCP marketplace tool function.
        
        Args:
            tool_schema: Tool schema object
            component_id: Component identifier
            component_type: Component type
            component_name: Component name
            mcp_metadata: MCP-specific metadata
            
        Returns:
            MCPTool instance
        """
        logger.debug(f"Generating MCP tool: {tool_schema.name}")
        
        return MCPTool(
            name=tool_schema.name,
            description=tool_schema.description,
            schema=tool_schema,
            component_id=component_id,
            component_type=component_type,
            mcp_metadata=mcp_metadata
        )
    
    def validate_tool_schema(self, tool_schema: Dict[str, Any]) -> bool:
        """
        Validate a tool schema dictionary.
        
        Args:
            tool_schema: Tool schema dictionary to validate
            
        Returns:
            True if schema is valid, False otherwise
        """
        try:
            # Check required top-level fields
            if "tool_type" not in tool_schema:
                logger.error("Missing tool_type in tool schema")
                return False
            
            if "component" not in tool_schema:
                logger.error("Missing component in tool schema")
                return False
            
            tool_type = tool_schema["tool_type"]
            if tool_type not in self.supported_tool_types:
                logger.error(f"Unsupported tool type: {tool_type}")
                return False
            
            component = tool_schema["component"]
            
            # Check required component fields
            required_component_fields = ["component_id", "component_type", "component_schema"]
            for field in required_component_fields:
                if field not in component:
                    logger.error(f"Missing required component field: {field}")
                    return False
            
            # Check component schema structure
            component_schema = component["component_schema"]
            required_schema_fields = ["name", "description", "parameters"]
            for field in required_schema_fields:
                if field not in component_schema:
                    logger.error(f"Missing required schema field: {field}")
                    return False
            
            # Validate parameters structure
            parameters = component_schema["parameters"]
            if not isinstance(parameters, dict):
                logger.error("Parameters must be a dictionary")
                return False
            
            if parameters.get("type") != "object":
                logger.error("Parameters type must be 'object'")
                return False
            
            if "properties" not in parameters:
                logger.error("Parameters must have 'properties' field")
                return False
            
            # Additional validation for MCP tools
            if tool_type == "mcp_marketplace":
                if "mcp_metadata" not in component:
                    logger.error("MCP tools must have mcp_metadata")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating tool schema: {str(e)}")
            return False
    
    def get_generation_statistics(self) -> Dict[str, Any]:
        """
        Get tool generation statistics.
        
        Returns:
            Dictionary containing generation statistics
        """
        return self.generation_stats.copy()
    
    def reset_statistics(self) -> None:
        """Reset generation statistics."""
        self.generation_stats = {
            "total_generated": 0,
            "successful_generations": 0,
            "failed_generations": 0,
            "average_generation_time": 0.0
        }
        logger.info("Tool generation statistics reset")
    
    def get_supported_tool_types(self) -> List[str]:
        """
        Get list of supported tool types.
        
        Returns:
            List of supported tool type strings
        """
        return self.supported_tool_types.copy()
    
    def add_tool_type_support(self, tool_type: str) -> None:
        """
        Add support for a new tool type.
        
        Args:
            tool_type: New tool type to support
        """
        if tool_type not in self.supported_tool_types:
            self.supported_tool_types.append(tool_type)
            logger.info(f"Added support for tool type: {tool_type}")
    
    def remove_tool_type_support(self, tool_type: str) -> bool:
        """
        Remove support for a tool type.
        
        Args:
            tool_type: Tool type to remove support for
            
        Returns:
            True if tool type was removed, False if it wasn't supported
        """
        if tool_type in self.supported_tool_types:
            self.supported_tool_types.remove(tool_type)
            logger.info(f"Removed support for tool type: {tool_type}")
            return True
        return False


# Global tool function generator instance
_tool_function_generator = None


def get_tool_function_generator() -> ToolFunctionGenerator:
    """
    Get or create the global tool function generator instance.
    
    Returns:
        The tool function generator instance
    """
    global _tool_function_generator
    if _tool_function_generator is None:
        logger.info("Creating new global ToolFunctionGenerator instance")
        _tool_function_generator = ToolFunctionGenerator()
    return _tool_function_generator
