"""
Agent Tools Models - Data models for agent tool functionality
"""

import asyncio
import logging
import time
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass, field
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)


@dataclass
class ToolSchema:
    """
    Schema definition for agent tools following AutoGen format.
    """
    name: str
    description: str
    parameters: Dict[str, Any]
    strict: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert tool schema to dictionary format."""
        return {
            "name": self.name,
            "description": self.description,
            "parameters": self.parameters,
            "strict": self.strict
        }
    
    def validate(self) -> bool:
        """
        Validate the tool schema format.
        
        Returns:
            True if schema is valid, False otherwise
        """
        try:
            # Check required fields
            if not self.name or not self.description:
                return False
            
            # Check parameters structure
            if not isinstance(self.parameters, dict):
                return False
            
            if self.parameters.get("type") != "object":
                return False
            
            # Check properties exist
            if "properties" not in self.parameters:
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating tool schema: {str(e)}")
            return False


class BaseTool(ABC):
    """
    Base class for all agent tools.
    """
    
    def __init__(
        self,
        name: str,
        description: str,
        schema: ToolSchema,
        component_id: str,
        component_type: str
    ):
        self.name = name
        self.description = description
        self.schema = schema
        self.component_id = component_id
        self.component_type = component_type
        self.execution_count = 0
        self.last_execution_time = None
    
    @abstractmethod
    async def execute(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the tool with given parameters.
        
        Args:
            parameters: Tool execution parameters
            
        Returns:
            Execution result dictionary
        """
        pass
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert tool to dictionary representation."""
        return {
            "name": self.name,
            "description": self.description,
            "schema": self.schema.to_dict(),
            "component_id": self.component_id,
            "component_type": self.component_type,
            "execution_count": self.execution_count,
            "last_execution_time": self.last_execution_time
        }


class AgentTool(BaseTool):
    """
    Standard workflow component tool for agent execution.
    """
    
    def __init__(
        self,
        name: str,
        description: str,
        schema: ToolSchema,
        component_id: str,
        component_type: str,
        execution_endpoint: Optional[str] = None
    ):
        super().__init__(name, description, schema, component_id, component_type)
        self.execution_endpoint = execution_endpoint
    
    async def execute(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the workflow component tool.
        
        Args:
            parameters: Tool execution parameters
            
        Returns:
            Execution result dictionary
        """
        try:
            import time
            start_time = time.time()
            
            logger.info(f"Executing agent tool: {self.name} with component_id: {self.component_id}")
            
            # Validate parameters against schema
            if not self._validate_parameters(parameters):
                return {
                    "status": "error",
                    "error": "Invalid parameters for tool execution",
                    "tool_name": self.name
                }
            
            # Execute the component
            result = await self._execute_component(parameters)
            
            # Update execution statistics
            self.execution_count += 1
            self.last_execution_time = time.time()
            
            execution_time = time.time() - start_time
            logger.info(f"Agent tool {self.name} executed successfully in {execution_time:.2f}s")
            
            return {
                "status": "success",
                "result": result,
                "tool_name": self.name,
                "execution_time": execution_time
            }
            
        except Exception as e:
            error_msg = f"Error executing agent tool {self.name}: {str(e)}"
            logger.error(error_msg)
            return {
                "status": "error",
                "error": error_msg,
                "tool_name": self.name
            }
    
    async def _execute_component(self, parameters: Dict[str, Any]) -> Any:
        """
        Execute the underlying workflow component.
        
        Args:
            parameters: Component execution parameters
            
        Returns:
            Component execution result
        """
        # This would typically call the node executor service
        # For now, return a mock result
        logger.debug(f"Executing component {self.component_type} with parameters: {parameters}")
        
        # Simulate component execution
        await asyncio.sleep(0.01)  # Small delay to simulate processing
        
        return {
            "component_id": self.component_id,
            "component_type": self.component_type,
            "processed_data": parameters,
            "timestamp": time.time()
        }
    
    def _validate_parameters(self, parameters: Dict[str, Any]) -> bool:
        """
        Validate parameters against tool schema.
        
        Args:
            parameters: Parameters to validate
            
        Returns:
            True if parameters are valid, False otherwise
        """
        try:
            schema_params = self.schema.parameters
            required_fields = schema_params.get("required", [])
            properties = schema_params.get("properties", {})
            
            # Check required fields
            for field in required_fields:
                if field not in parameters:
                    logger.error(f"Missing required field: {field}")
                    return False
            
            # Check field types (basic validation)
            for field, value in parameters.items():
                if field in properties:
                    expected_type = properties[field].get("type")
                    if expected_type and not self._check_type(value, expected_type):
                        logger.error(f"Invalid type for field {field}: expected {expected_type}")
                        return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating parameters: {str(e)}")
            return False
    
    def _check_type(self, value: Any, expected_type: str) -> bool:
        """Check if value matches expected type."""
        type_mapping = {
            "string": str,
            "integer": int,
            "number": (int, float),
            "boolean": bool,
            "array": list,
            "object": dict
        }
        
        expected_python_type = type_mapping.get(expected_type)
        if expected_python_type:
            return isinstance(value, expected_python_type)
        
        return True  # Unknown type, assume valid


class MCPTool(AgentTool):
    """
    MCP (Model Context Protocol) marketplace tool for agent execution.
    """
    
    def __init__(
        self,
        name: str,
        description: str,
        schema: ToolSchema,
        component_id: str,
        component_type: str,
        mcp_metadata: Dict[str, Any],
        execution_endpoint: Optional[str] = None
    ):
        super().__init__(name, description, schema, component_id, component_type, execution_endpoint)
        self.mcp_metadata = mcp_metadata
    
    async def _execute_component(self, parameters: Dict[str, Any]) -> Any:
        """
        Execute the MCP marketplace component.
        
        Args:
            parameters: Component execution parameters
            
        Returns:
            MCP component execution result
        """
        logger.debug(f"Executing MCP component {self.component_type} with metadata: {self.mcp_metadata}")
        
        # Simulate MCP component execution
        await asyncio.sleep(0.02)  # Slightly longer delay for MCP components
        
        return {
            "component_id": self.component_id,
            "component_type": self.component_type,
            "mcp_metadata": self.mcp_metadata,
            "processed_data": parameters,
            "timestamp": time.time(),
            "mcp_server_url": self.mcp_metadata.get("server_url"),
            "mcp_tool_name": self.mcp_metadata.get("tool_name")
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert MCP tool to dictionary representation."""
        base_dict = super().to_dict()
        base_dict["mcp_metadata"] = self.mcp_metadata
        base_dict["tool_type"] = "mcp_marketplace"
        return base_dict


@dataclass
class AgentToolRegistry:
    """
    Registry for managing agent tools.
    """
    agent_id: str
    tools: List[Union[AgentTool, MCPTool]] = field(default_factory=list)
    created_at: float = field(default_factory=time.time)
    updated_at: float = field(default_factory=time.time)
    
    def add_tool(self, tool: Union[AgentTool, MCPTool]) -> None:
        """Add a tool to the registry."""
        self.tools.append(tool)
        self.updated_at = time.time()
        logger.info(f"Added tool {tool.name} to agent {self.agent_id}")
    
    def remove_tool(self, tool_name: str) -> bool:
        """Remove a tool from the registry."""
        for i, tool in enumerate(self.tools):
            if tool.name == tool_name:
                del self.tools[i]
                self.updated_at = time.time()
                logger.info(f"Removed tool {tool_name} from agent {self.agent_id}")
                return True
        return False
    
    def get_tool(self, tool_name: str) -> Optional[Union[AgentTool, MCPTool]]:
        """Get a tool by name."""
        for tool in self.tools:
            if tool.name == tool_name:
                return tool
        return None
    
    def get_tool_names(self) -> List[str]:
        """Get list of all tool names."""
        return [tool.name for tool in self.tools]
    
    def clear_tools(self) -> None:
        """Clear all tools from the registry."""
        self.tools.clear()
        self.updated_at = time.time()
        logger.info(f"Cleared all tools from agent {self.agent_id}")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert registry to dictionary representation."""
        return {
            "agent_id": self.agent_id,
            "tools": [tool.to_dict() for tool in self.tools],
            "tool_count": len(self.tools),
            "created_at": self.created_at,
            "updated_at": self.updated_at
        }
