"""
Agent Executor Service - Consumes tool schemas from Kafka and manages agent tool functions
"""

import asyncio
import logging
import json
import time
from typing import Dict, Any, List, Optional, Union

from app.models.agent_tools import AgentTool, MCPTool, AgentToolRegistry
from app.utils.tool_function_generator import get_tool_function_generator, ToolFunctionGenerator

logger = logging.getLogger(__name__)


class AgentExecutor:
    """
    Agent executor service for consuming tool schemas from Kafka messages
    and creating agent tool functions.
    
    This service handles:
    - Consuming tool schemas from Kafka messages
    - Creating agent tool functions from component schemas
    - Managing agent tool registries
    - Executing agent tools when called
    """
    
    def __init__(self, tool_function_generator: ToolFunctionGenerator = None):
        """
        Initialize the agent executor.
        
        Args:
            tool_function_generator: Optional tool function generator instance
        """
        self.tool_function_generator = tool_function_generator or get_tool_function_generator()
        self.active_agents: Dict[str, Dict[str, Any]] = {}
        self.tool_registry: Dict[str, List[Union[AgentTool, MCPTool]]] = {}
        self.processing_stats = {
            "total_messages_processed": 0,
            "successful_processing": 0,
            "failed_processing": 0,
            "total_tools_created": 0,
            "average_processing_time": 0.0
        }
        
        logger.info("AgentExecutor initialized")
    
    async def process_kafka_message(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a Kafka message containing agent configuration with tool schemas.
        
        Args:
            message: Kafka message dictionary containing agent_config
            
        Returns:
            Processing result dictionary
        """
        start_time = time.time()
        
        try:
            logger.info(f"Processing Kafka message for request_id: {message.get('request_id', 'unknown')}")
            
            # Extract agent configuration
            agent_config = message.get("agent_config", {})
            if not agent_config:
                error_msg = "No agent_config found in Kafka message"
                logger.error(error_msg)
                self._update_processing_stats(False, time.time() - start_time)
                return {"status": "error", "error": error_msg}
            
            agent_id = agent_config.get("id")
            if not agent_id:
                error_msg = "No agent ID found in agent_config"
                logger.error(error_msg)
                self._update_processing_stats(False, time.time() - start_time)
                return {"status": "error", "error": error_msg}
            
            # Extract tool schemas
            tool_schemas = agent_config.get("tools", [])
            logger.info(f"Processing {len(tool_schemas)} tool schemas for agent {agent_id}")
            
            # Generate tool functions
            tool_functions = []
            if tool_schemas:
                tool_functions = self.tool_function_generator.generate_tool_functions(tool_schemas)
                logger.info(f"Generated {len(tool_functions)} tool functions for agent {agent_id}")
            
            # Register agent and tools
            self.register_agent(agent_config)
            self.tool_registry[agent_id] = tool_functions
            
            # Update statistics
            processing_time = time.time() - start_time
            self._update_processing_stats(True, processing_time)
            self.processing_stats["total_tools_created"] += len(tool_functions)
            
            # Determine result status
            if len(tool_schemas) == 0:
                status = "success"  # No tools to process is success
            elif len(tool_functions) == len(tool_schemas):
                status = "success"  # All tools processed successfully
            elif len(tool_functions) > 0:
                status = "partial_success"  # Some tools processed successfully
            else:
                status = "partial_success"  # No tools processed successfully
            
            logger.info(f"Successfully processed Kafka message for agent {agent_id} in {processing_time:.3f}s")
            
            return {
                "status": status,
                "agent_id": agent_id,
                "tools_processed": len(tool_schemas),
                "tools_created": len(tool_functions),
                "processing_time": processing_time
            }
            
        except Exception as e:
            error_msg = f"Error processing Kafka message: {str(e)}"
            logger.error(error_msg)
            processing_time = time.time() - start_time
            self._update_processing_stats(False, processing_time)
            
            return {
                "status": "error",
                "error": error_msg,
                "processing_time": processing_time
            }
    
    def register_agent(self, agent_config: Dict[str, Any]) -> None:
        """
        Register an agent configuration.
        
        Args:
            agent_config: Agent configuration dictionary
        """
        agent_id = agent_config.get("id")
        if not agent_id:
            raise ValueError("Agent configuration must have an 'id' field")
        
        self.active_agents[agent_id] = {
            "config": agent_config,
            "registered_at": time.time(),
            "last_updated": time.time()
        }
        
        logger.info(f"Registered agent: {agent_id}")
    
    def unregister_agent(self, agent_id: str) -> bool:
        """
        Unregister an agent and clean up its tools.
        
        Args:
            agent_id: Agent identifier
            
        Returns:
            True if agent was unregistered, False if not found
        """
        if agent_id in self.active_agents:
            del self.active_agents[agent_id]
            
            if agent_id in self.tool_registry:
                del self.tool_registry[agent_id]
            
            logger.info(f"Unregistered agent: {agent_id}")
            return True
        
        return False
    
    def get_agent_tool_functions(self, agent_id: str) -> List[Union[AgentTool, MCPTool]]:
        """
        Get tool functions for a specific agent.
        
        Args:
            agent_id: Agent identifier
            
        Returns:
            List of tool functions for the agent
        """
        return self.tool_registry.get(agent_id, [])
    
    async def execute_agent_tool(
        self,
        agent_id: str,
        tool_name: str,
        parameters: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Execute a specific tool for an agent.
        
        Args:
            agent_id: Agent identifier
            tool_name: Name of the tool to execute
            parameters: Tool execution parameters
            
        Returns:
            Tool execution result
        """
        try:
            # Get agent tools
            agent_tools = self.get_agent_tool_functions(agent_id)
            if not agent_tools:
                return {
                    "status": "error",
                    "error": f"No tools found for agent {agent_id}"
                }
            
            # Find the specific tool
            target_tool = None
            for tool in agent_tools:
                if tool.name == tool_name:
                    target_tool = tool
                    break
            
            if not target_tool:
                return {
                    "status": "error",
                    "error": f"Tool {tool_name} not found for agent {agent_id}"
                }
            
            # Execute the tool
            logger.info(f"Executing tool {tool_name} for agent {agent_id}")
            result = await target_tool.execute(parameters)
            
            return result
            
        except Exception as e:
            error_msg = f"Error executing tool {tool_name} for agent {agent_id}: {str(e)}"
            logger.error(error_msg)
            return {
                "status": "error",
                "error": error_msg
            }
    
    def get_agent_registry(self, agent_id: str) -> Optional[AgentToolRegistry]:
        """
        Get the tool registry for a specific agent.
        
        Args:
            agent_id: Agent identifier
            
        Returns:
            AgentToolRegistry instance or None if not found
        """
        if agent_id not in self.active_agents:
            return None
        
        tools = self.get_agent_tool_functions(agent_id)
        registry = AgentToolRegistry(agent_id=agent_id)
        
        for tool in tools:
            registry.add_tool(tool)
        
        return registry
    
    def get_processing_statistics(self) -> Dict[str, Any]:
        """
        Get message processing statistics.
        
        Returns:
            Dictionary containing processing statistics
        """
        return self.processing_stats.copy()
    
    def get_active_agents(self) -> Dict[str, Dict[str, Any]]:
        """
        Get all active agents.
        
        Returns:
            Dictionary of active agent configurations
        """
        return self.active_agents.copy()
    
    def get_agent_tool_count(self, agent_id: str) -> int:
        """
        Get the number of tools for a specific agent.
        
        Args:
            agent_id: Agent identifier
            
        Returns:
            Number of tools for the agent
        """
        return len(self.get_agent_tool_functions(agent_id))
    
    def get_total_tool_count(self) -> int:
        """
        Get the total number of tools across all agents.
        
        Returns:
            Total number of tools
        """
        return sum(len(tools) for tools in self.tool_registry.values())
    
    def _update_processing_stats(self, success: bool, processing_time: float) -> None:
        """
        Update processing statistics.
        
        Args:
            success: Whether the processing was successful
            processing_time: Time taken for processing
        """
        self.processing_stats["total_messages_processed"] += 1
        
        if success:
            self.processing_stats["successful_processing"] += 1
        else:
            self.processing_stats["failed_processing"] += 1
        
        # Update average processing time
        total = self.processing_stats["total_messages_processed"]
        current_avg = self.processing_stats["average_processing_time"]
        self.processing_stats["average_processing_time"] = (
            (current_avg * (total - 1) + processing_time) / total
        )
    
    def reset_statistics(self) -> None:
        """Reset processing statistics."""
        self.processing_stats = {
            "total_messages_processed": 0,
            "successful_processing": 0,
            "failed_processing": 0,
            "total_tools_created": 0,
            "average_processing_time": 0.0
        }
        logger.info("Processing statistics reset")
    
    def clear_all_agents(self) -> None:
        """Clear all registered agents and their tools."""
        self.active_agents.clear()
        self.tool_registry.clear()
        logger.info("Cleared all registered agents and tools")


# Global agent executor instance
_agent_executor = None


def get_agent_executor() -> AgentExecutor:
    """
    Get or create the global agent executor instance.
    
    Returns:
        The agent executor instance
    """
    global _agent_executor
    if _agent_executor is None:
        logger.info("Creating new global AgentExecutor instance")
        _agent_executor = AgentExecutor()
    return _agent_executor
