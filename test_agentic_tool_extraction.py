#!/usr/bin/env python3
"""
Test script to verify that AgenticAI tool connections are properly extracted
and converted to separate transition nodes in the workflow schema converter.
"""

import json
import sys
import os

# Add the workflow-service directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'workflow-service'))

from app.services.workflow_builder.workflow_schema_converter import (
    convert_workflow_to_transition_schema,
    extract_agentic_tool_connections
)

def test_agentic_tool_extraction():
    """Test the extraction of tool connections from AgenticAI nodes."""
    
    print("🧪 TESTING AGENTIC AI TOOL EXTRACTION")
    print("="*50)
    
    # Load the agentic_schema.json file
    try:
        with open('agentic_schema.json', 'r') as f:
            workflow_data = json.load(f)
        print("✅ Loaded agentic_schema.json successfully")
    except Exception as e:
        print(f"❌ Failed to load agentic_schema.json: {e}")
        return False
    
    # Extract workflow data properly
    workflow_nodes = workflow_data.get("workflow_data", {}).get("nodes", [])
    workflow_edges = workflow_data.get("workflow_data", {}).get("edges", [])
    
    print(f"\n📊 ORIGINAL WORKFLOW:")
    print(f"   - Nodes: {len(workflow_nodes)}")
    print(f"   - Edges: {len(workflow_edges)}")
    
    # Find AgenticAI nodes and their tool connections
    agentic_nodes = []
    total_tool_connections = 0
    
    for node in workflow_nodes:
        if (node.get("data", {}).get("definition", {}).get("name") == "AgenticAI" or
            node.get("data", {}).get("originalType") == "AgenticAI" or
            node.get("data", {}).get("type") == "agent"):
            agentic_nodes.append(node)
            tool_connections = node.get("data", {}).get("config", {}).get("tool_connections", {})
            
            # Count tools across all handles
            for handle, tools in tool_connections.items():
                if isinstance(tools, list):
                    total_tool_connections += len(tools)
    
    print(f"\n🤖 AGENTIC AI ANALYSIS:")
    print(f"   - AgenticAI nodes found: {len(agentic_nodes)}")
    print(f"   - Total tool connections: {total_tool_connections}")
    
    for i, node in enumerate(agentic_nodes):
        node_id = node.get("id")
        tool_connections = node.get("data", {}).get("config", {}).get("tool_connections", {})
        
        print(f"   - Node {i+1}: {node_id}")
        node_total_tools = 0
        for handle, tools in tool_connections.items():
            if isinstance(tools, list):
                node_total_tools += len(tools)
                print(f"     Handle '{handle}': {len(tools)} tools")
                for j, tool in enumerate(tools):
                    tool_id = tool.get('node_id', 'Unknown')
                    tool_type = tool.get('node_type', 'unknown')
                    print(f"       {j+1}. {tool_id} ({tool_type})")
        print(f"     Total tools: {node_total_tools}")
    
    # Test the extraction function
    print(f"\n🔧 TESTING TOOL EXTRACTION...")
    try:
        virtual_tool_nodes, virtual_tool_edges = extract_agentic_tool_connections(workflow_nodes)
        print(f"✅ Tool extraction successful")
        print(f"   - Virtual tool nodes created: {len(virtual_tool_nodes)}")
        print(f"   - Virtual tool edges created: {len(virtual_tool_edges)}")
        
        # Show details of created virtual nodes
        for i, vnode in enumerate(virtual_tool_nodes):
            print(f"   - Virtual Node {i+1}: {vnode.get('id')}")
            print(f"     Name: {vnode.get('data', {}).get('definition', {}).get('name')}")
            print(f"     Type: {vnode.get('data', {}).get('type')}")
            print(f"     Parent: {vnode.get('_parent_agentic_node')}")
        
    except Exception as e:
        print(f"❌ Tool extraction failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # Test the full conversion
    print(f"\n🔄 TESTING FULL SCHEMA CONVERSION...")
    try:
        # Extract the actual workflow data structure expected by the converter
        conversion_data = {
            "nodes": workflow_nodes,
            "edges": workflow_edges,
            "mcp_configs": workflow_data.get("mcp_configs", [])
        }
        transition_schema = convert_workflow_to_transition_schema(conversion_data)
        print(f"✅ Schema conversion successful")
        
        # Analyze the results
        transition_nodes = transition_schema.get("nodes", [])
        transitions = transition_schema.get("transitions", [])
        
        print(f"\n📊 CONVERSION RESULTS:")
        print(f"   - Transition nodes: {len(transition_nodes)}")
        print(f"   - Transitions: {len(transitions)}")
        
        # Check if virtual tool nodes were converted
        virtual_tool_transition_nodes = [
            node for node in transition_nodes 
            if any(vnode.get('id') == node.get('id') for vnode in virtual_tool_nodes)
        ]
        
        print(f"   - Virtual tool nodes in final schema: {len(virtual_tool_transition_nodes)}")
        
        # Check AgenticAI transitions
        agentic_transitions = []
        for transition in transitions:
            node_id = transition.get("node_info", {}).get("node_id", "")
            # Check if this transition corresponds to an AgenticAI node
            for agentic_node in agentic_nodes:
                agentic_node_name = agentic_node.get("data", {}).get("definition", {}).get("name", "")
                if node_id == agentic_node_name or node_id == agentic_node.get("id"):
                    agentic_transitions.append(transition)
                    break
        
        print(f"   - AgenticAI transitions: {len(agentic_transitions)}")
        
        # Show tool transitions
        tool_transitions = []
        for transition in transitions:
            transition_id = transition.get("id", "")
            # Check if this transition corresponds to a virtual tool
            for vnode in virtual_tool_nodes:
                if f"transition-{vnode.get('id')}" == transition_id:
                    tool_transitions.append(transition)
                    break
        
        print(f"   - Tool transitions: {len(tool_transitions)}")
        
        # Detailed analysis
        print(f"\n🔍 DETAILED ANALYSIS:")
        
        if agentic_transitions:
            print(f"   AgenticAI Transitions:")
            for i, transition in enumerate(agentic_transitions):
                tools_to_use = transition.get("node_info", {}).get("tools_to_use", [])
                input_data = transition.get("node_info", {}).get("input_data", [])
                print(f"     {i+1}. {transition.get('id')}")
                print(f"        Tools to use: {len(tools_to_use)}")
                print(f"        Input data: {len(input_data)}")
        
        if tool_transitions:
            print(f"   Tool Transitions:")
            for i, transition in enumerate(tool_transitions):
                tools_to_use = transition.get("node_info", {}).get("tools_to_use", [])
                print(f"     {i+1}. {transition.get('id')}")
                print(f"        Tools to use: {len(tools_to_use)}")
                if tools_to_use:
                    print(f"        Tool name: {tools_to_use[0].get('tool_name', 'Unknown')}")
        
        # Success metrics
        expected_tool_nodes = total_tool_connections
        actual_tool_nodes = len(virtual_tool_transition_nodes)
        actual_tool_transitions = len(tool_transitions)
        
        print(f"\n✅ SUCCESS METRICS:")
        print(f"   - Expected tool nodes: {expected_tool_nodes}")
        print(f"   - Actual tool nodes: {actual_tool_nodes}")
        print(f"   - Actual tool transitions: {actual_tool_transitions}")
        
        success = (actual_tool_nodes == expected_tool_nodes and 
                  actual_tool_transitions == expected_tool_nodes)
        
        if success:
            print(f"   🎉 ALL TOOLS SUCCESSFULLY CONVERTED TO SEPARATE TRANSITIONS!")
        else:
            print(f"   ⚠️  Some tools may not have been converted properly")
        
        return success
        
    except Exception as e:
        print(f"❌ Schema conversion failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_agentic_tool_extraction()
    if success:
        print(f"\n🎉 TEST PASSED: AgenticAI tool extraction working correctly!")
        sys.exit(0)
    else:
        print(f"\n❌ TEST FAILED: Issues found with tool extraction")
        sys.exit(1)