/**
 * Complete end-to-end test for legacy format conversion and multiple tools per handle
 * This test validates the entire workflow from connection creation to execution processing
 */

// Test the complete flow: Legacy → New → Multiple connections → Processing
function testCompleteConversionFlow() {
  console.log("🚀 Complete Conversion Flow Test");
  console.log("=" .repeat(60));
  
  // Step 1: Start with legacy format data (simulating existing workflow)
  console.log("\n📋 Step 1: Legacy Format Workflow");
  console.log("-" .repeat(40));
  
  const legacyWorkflow = {
    nodes: [
      {
        id: "start-node",
        data: { 
          originalType: "StartNode",
          config: { collected_parameters: {} }
        }
      },
      {
        id: "agentic-ai-1",
        data: {
          originalType: "AgenticAI",
          label: "AI Agent",
          config: { 
            num_tool_handles: 2,
            // Legacy format - single objects
            tool_connections: {
              "tool_1": {
                node_id: "text-component-1",
                node_type: "TextGeneratorComponent",
                node_label: "Text Generator 1",
                component_definition: { name: "TextGeneratorComponent" }
              }
            }
          }
        }
      }
    ],
    edges: []
  };
  
  console.log("Legacy tool_connections format:");
  console.log(JSON.stringify(legacyWorkflow.nodes[1].data.config.tool_connections, null, 2));
  
  // Step 2: Simulate adding a new connection (should convert to array format)
  console.log("\n📋 Step 2: Adding New Connection (Conversion Trigger)");
  console.log("-" .repeat(40));
  
  // Simulate the enhanced onConnect logic
  const newConnection = {
    source: "text-component-2",
    target: "agentic-ai-1", 
    targetHandle: "tool_1" // Same handle as existing connection!
  };
  
  console.log(`Adding connection: ${newConnection.source} -> ${newConnection.targetHandle}`);
  
  // Find the target node
  const targetNode = legacyWorkflow.nodes.find(n => n.id === newConnection.target);
  const sourceNode = { 
    id: "text-component-2", 
    data: { 
      originalType: "TextGeneratorComponent", 
      label: "Text Generator 2",
      definition: { name: "TextGeneratorComponent" }
    } 
  };
  
  if (targetNode) {
    const currentConfig = targetNode.data.config || {};
    const toolConnections = currentConfig.tool_connections || {};
    
    // Support multiple tools per handle - store as array
    if (!toolConnections[newConnection.targetHandle]) {
      toolConnections[newConnection.targetHandle] = [];
    }
    
    // Check if we need to convert legacy format
    if (!Array.isArray(toolConnections[newConnection.targetHandle])) {
      console.log("🔄 Converting legacy format to array format");
      const legacyConnection = toolConnections[newConnection.targetHandle];
      toolConnections[newConnection.targetHandle] = [legacyConnection];
      console.log("   Legacy connection preserved in array");
    }
    
    // Add new connection
    const newToolConnection = {
      node_id: newConnection.source,
      node_type: sourceNode.data.originalType,
      node_label: sourceNode.data.label,
      component_definition: sourceNode.data.definition
    };
    
    toolConnections[newConnection.targetHandle].push(newToolConnection);
    console.log(`   New connection added. Total connections for ${newConnection.targetHandle}: ${toolConnections[newConnection.targetHandle].length}`);
    
    // Update the node
    targetNode.data.config.tool_connections = toolConnections;
  }
  
  console.log("\nAfter conversion and addition:");
  console.log(JSON.stringify(targetNode.data.config.tool_connections, null, 2));
  
  // Step 3: Add another connection to a different handle
  console.log("\n📋 Step 3: Adding Connection to Different Handle");
  console.log("-" .repeat(40));
  
  const anotherConnection = {
    source: "data-component-1",
    target: "agentic-ai-1",
    targetHandle: "tool_2"
  };
  
  console.log(`Adding connection: ${anotherConnection.source} -> ${anotherConnection.targetHandle}`);
  
  const anotherSourceNode = {
    id: "data-component-1",
    data: {
      originalType: "DataProcessorComponent",
      label: "Data Processor", 
      definition: { name: "DataProcessorComponent" }
    }
  };
  
  // Add to tool_2
  if (!targetNode.data.config.tool_connections[anotherConnection.targetHandle]) {
    targetNode.data.config.tool_connections[anotherConnection.targetHandle] = [];
  }
  
  targetNode.data.config.tool_connections[anotherConnection.targetHandle].push({
    node_id: anotherConnection.source,
    node_type: anotherSourceNode.data.originalType,
    node_label: anotherSourceNode.data.label,
    component_definition: anotherSourceNode.data.definition
  });
  
  console.log("Final tool_connections state:");
  console.log(JSON.stringify(targetNode.data.config.tool_connections, null, 2));
  
  // Step 4: Simulate workflow processing
  console.log("\n📋 Step 4: Workflow Processing");
  console.log("-" .repeat(40));
  
  const agenticAINodes = legacyWorkflow.nodes.filter(node => node.data.originalType === "AgenticAI");
  const collectedParameters = {};
  
  agenticAINodes.forEach(agenticNode => {
    if (agenticNode.data.config?.tool_connections) {
      const toolConnections = agenticNode.data.config.tool_connections;
      const processedToolConnections = {};
      
      Object.entries(toolConnections).forEach(([handle, connections]) => {
        if (Array.isArray(connections)) {
          processedToolConnections[handle] = connections;
          console.log(`✅ Handle ${handle}: ${connections.length} tool connections (array format)`);
        } else {
          processedToolConnections[handle] = [connections];
          console.log(`🔄 Handle ${handle}: 1 tool connection (converted from legacy)`);
        }
      });
      
      collectedParameters[`${agenticNode.id}_tool_connections`] = {
        node_id: agenticNode.id,
        node_name: agenticNode.data.label,
        input_name: "tool_connections",
        value: processedToolConnections,
        is_tool_connection_data: true,
      };
    }
  });
  
  console.log("\nProcessed for execution:");
  console.log(JSON.stringify(collectedParameters, null, 2));
  
  // Step 5: Verification
  console.log("\n📋 Step 5: Verification");
  console.log("-" .repeat(40));
  
  const processedConnections = collectedParameters["agentic-ai-1_tool_connections"]?.value;
  
  const verifications = [
    {
      test: "tool_1 has 2 connections",
      result: Array.isArray(processedConnections?.tool_1) && processedConnections.tool_1.length === 2
    },
    {
      test: "tool_2 has 1 connection", 
      result: Array.isArray(processedConnections?.tool_2) && processedConnections.tool_2.length === 1
    },
    {
      test: "Legacy connection preserved",
      result: processedConnections?.tool_1?.[0]?.node_label === "Text Generator 1"
    },
    {
      test: "New connection added",
      result: processedConnections?.tool_1?.[1]?.node_label === "Text Generator 2"
    },
    {
      test: "All connections are arrays",
      result: Object.values(processedConnections || {}).every(conn => Array.isArray(conn))
    }
  ];
  
  console.log("Verification Results:");
  let allPassed = true;
  verifications.forEach(verification => {
    const status = verification.result ? '✅' : '❌';
    console.log(`  ${status} ${verification.test}`);
    if (!verification.result) allPassed = false;
  });
  
  return {
    success: allPassed,
    finalState: processedConnections,
    verifications
  };
}

// Test edge removal with converted format
function testEdgeRemovalAfterConversion() {
  console.log("\n🗑️  Testing Edge Removal After Conversion");
  console.log("=" .repeat(50));
  
  // Start with converted format (arrays)
  let toolConnections = {
    "tool_1": [
      {
        node_id: "text-component-1",
        node_type: "TextGeneratorComponent",
        node_label: "Text Generator 1 (Legacy)",
        component_definition: { name: "TextGeneratorComponent" }
      },
      {
        node_id: "text-component-2",
        node_type: "TextGeneratorComponent", 
        node_label: "Text Generator 2 (New)",
        component_definition: { name: "TextGeneratorComponent" }
      }
    ]
  };
  
  console.log("Initial state after conversion:");
  console.log(`tool_1: ${toolConnections.tool_1.length} connections`);
  toolConnections.tool_1.forEach((conn, i) => {
    console.log(`  ${i + 1}. ${conn.node_label}`);
  });
  
  // Remove the legacy connection
  const edgeToRemove = {
    source: "text-component-1",
    targetHandle: "tool_1"
  };
  
  console.log(`\nRemoving: ${edgeToRemove.source} from ${edgeToRemove.targetHandle}`);
  
  // Simulate removal logic
  if (toolConnections[edgeToRemove.targetHandle]) {
    if (Array.isArray(toolConnections[edgeToRemove.targetHandle])) {
      toolConnections[edgeToRemove.targetHandle] = toolConnections[edgeToRemove.targetHandle].filter(
        (conn) => conn.node_id !== edgeToRemove.source
      );
      
      if (toolConnections[edgeToRemove.targetHandle].length === 0) {
        delete toolConnections[edgeToRemove.targetHandle];
      }
    }
  }
  
  console.log("\nAfter removal:");
  if (toolConnections.tool_1) {
    console.log(`tool_1: ${toolConnections.tool_1.length} connections`);
    toolConnections.tool_1.forEach((conn, i) => {
      console.log(`  ${i + 1}. ${conn.node_label}`);
    });
  } else {
    console.log("tool_1: handle removed (no connections)");
  }
  
  const success = toolConnections.tool_1?.length === 1 && 
                 toolConnections.tool_1[0].node_label === "Text Generator 2 (New)";
  
  console.log(`\nRemoval success: ${success ? '✅' : '❌'}`);
  
  return { success, finalState: toolConnections };
}

// Run complete test suite
console.log("🧪 Starting Complete Conversion Flow Tests\n");

const flowTest = testCompleteConversionFlow();
const removalTest = testEdgeRemovalAfterConversion();

console.log("\n📈 Complete Test Summary:");
console.log("=" .repeat(40));
console.log(`Conversion Flow: ${flowTest.success ? '✅ PASS' : '❌ FAIL'}`);
console.log(`Edge Removal: ${removalTest.success ? '✅ PASS' : '❌ FAIL'}`);

const overallSuccess = flowTest.success && removalTest.success;
console.log(`Overall: ${overallSuccess ? '✅ ALL TESTS PASS' : '❌ SOME TESTS FAILED'}`);

if (overallSuccess) {
  console.log("\n🎉 Complete conversion flow is working perfectly!");
  console.log("   ✅ Legacy format automatically converted to arrays");
  console.log("   ✅ Multiple tools can connect to same handle");
  console.log("   ✅ New connections properly added to arrays");
  console.log("   ✅ Processing logic handles all formats correctly");
  console.log("   ✅ Edge removal works with converted arrays");
  console.log("   ✅ Backward compatibility fully maintained");
} else {
  console.log("\n⚠️  Some issues detected in the conversion flow.");
  if (!flowTest.success) {
    console.log("   - Conversion flow issues detected");
  }
  if (!removalTest.success) {
    console.log("   - Edge removal issues detected");
  }
}