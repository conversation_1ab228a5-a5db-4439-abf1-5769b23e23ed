/**
 * Test to check if multiple tools can connect to the same tool handle
 * This should NOT be allowed - each tool handle should only accept one connection
 */

// Mock data for testing multiple connections to same handle
const mockNodes = [
  {
    id: "start-node",
    data: { originalType: "StartNode" }
  },
  {
    id: "agentic-ai-1",
    data: {
      originalType: "AgenticAI",
      config: { num_tool_handles: 2, tool_connections: {} }
    }
  },
  {
    id: "text-component-1",
    data: { originalType: "TextGeneratorComponent", label: "Text Generator 1" }
  },
  {
    id: "text-component-2", 
    data: { originalType: "TextGeneratorComponent", label: "Text Generator 2" }
  }
];

// Test multiple connections to same tool handle
function testMultipleConnectionsToSameHandle() {
  console.log("🧪 Testing Multiple Connections to Same Tool Handle");
  console.log("=" .repeat(60));
  
  // Simulate first connection: text-component-1 -> tool_1
  const firstConnection = {
    source: "text-component-1",
    target: "agentic-ai-1", 
    targetHandle: "tool_1"
  };
  
  // Simulate second connection: text-component-2 -> tool_1 (same handle!)
  const secondConnection = {
    source: "text-component-2",
    target: "agentic-ai-1",
    targetHandle: "tool_1" // Same handle as first connection
  };
  
  console.log("First connection:", firstConnection);
  console.log("Second connection:", secondConnection);
  
  // Simulate the current onConnect behavior
  let toolConnections = {};
  
  // First connection
  const sourceNode1 = mockNodes.find(n => n.id === firstConnection.source);
  toolConnections[firstConnection.targetHandle] = {
    node_id: firstConnection.source,
    node_type: sourceNode1.data.originalType,
    node_label: sourceNode1.data.label,
    component_definition: sourceNode1.data.definition
  };
  
  console.log("\nAfter first connection:");
  console.log("tool_connections:", toolConnections);
  
  // Second connection (overwrites first!)
  const sourceNode2 = mockNodes.find(n => n.id === secondConnection.source);
  toolConnections[secondConnection.targetHandle] = {
    node_id: secondConnection.source,
    node_type: sourceNode2.data.originalType, 
    node_label: sourceNode2.data.label,
    component_definition: sourceNode2.data.definition
  };
  
  console.log("\nAfter second connection (overwrites first!):");
  console.log("tool_connections:", toolConnections);
  
  // Check if first connection was lost
  const firstConnectionLost = toolConnections.tool_1.node_id !== "text-component-1";
  
  if (firstConnectionLost) {
    console.log("\n❌ ISSUE FOUND: First connection was overwritten!");
    console.log("   - text-component-1 connection to tool_1 was lost");
    console.log("   - text-component-2 connection to tool_1 replaced it");
    console.log("   - This could cause confusion and data loss");
  } else {
    console.log("\n✅ No issue found");
  }
  
  return firstConnectionLost;
}

// Run the test
const hasIssue = testMultipleConnectionsToSameHandle();

if (hasIssue) {
  console.log("\n🔧 RECOMMENDATION:");
  console.log("- Prevent multiple connections to the same tool handle");
  console.log("- Show warning when user tries to connect to occupied handle");
  console.log("- Either reject the connection or ask user to confirm replacement");
}