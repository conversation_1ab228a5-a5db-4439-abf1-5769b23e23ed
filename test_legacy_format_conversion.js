/**
 * Test to verify that legacy single-connection format is properly converted
 * to the new array-based format during processing
 */

// Mock legacy workflow data (single connection format)
const mockLegacyNodes = [
  {
    id: "start-node",
    data: { 
      originalType: "StartNode",
      config: { collected_parameters: {} }
    }
  },
  {
    id: "agentic-ai-1",
    data: {
      originalType: "AgenticAI",
      label: "AI Agent",
      config: { 
        num_tool_handles: 2, 
        // Legacy format - single objects instead of arrays
        tool_connections: {
          "tool_1": {
            node_id: "text-component-1",
            node_type: "TextGeneratorComponent",
            node_label: "Text Generator 1",
            component_definition: { name: "TextGeneratorComponent", type: "component" }
          },
          "tool_2": {
            node_id: "data-component-1", 
            node_type: "DataProcessorComponent",
            node_label: "Data Processor",
            component_definition: { name: "DataProcessorComponent", type: "component" }
          }
        }
      }
    }
  }
];

// Mock new workflow data (array format)
const mockNewNodes = [
  {
    id: "start-node",
    data: { 
      originalType: "StartNode",
      config: { collected_parameters: {} }
    }
  },
  {
    id: "agentic-ai-1",
    data: {
      originalType: "AgenticAI",
      label: "AI Agent",
      config: { 
        num_tool_handles: 2, 
        // New format - arrays of connections
        tool_connections: {
          "tool_1": [
            {
              node_id: "text-component-1",
              node_type: "TextGeneratorComponent",
              node_label: "Text Generator 1",
              component_definition: { name: "TextGeneratorComponent", type: "component" }
            }
          ],
          "tool_2": [
            {
              node_id: "data-component-1",
              node_type: "DataProcessorComponent", 
              node_label: "Data Processor",
              component_definition: { name: "DataProcessorComponent", type: "component" }
            }
          ]
        }
      }
    }
  }
];

// Simulate the conversion logic from workflow-api.ts
function simulateConversionLogic(nodes) {
  console.log("🔄 Simulating Legacy Format Conversion Logic");
  console.log("=" .repeat(50));
  
  const agenticAINodes = nodes.filter(node => node.data.originalType === "AgenticAI");
  const collectedParameters = {};
  
  agenticAINodes.forEach(agenticNode => {
    if (agenticNode.data.config?.tool_connections) {
      console.log(`\nProcessing AgenticAI node: ${agenticNode.id}`);
      
      // Process tool connections - handle both array and legacy single connection formats
      const toolConnections = agenticNode.data.config.tool_connections;
      const processedToolConnections = {};
      
      console.log("Original tool_connections:", JSON.stringify(toolConnections, null, 2));
      
      Object.entries(toolConnections).forEach(([handle, connections]) => {
        console.log(`\nProcessing handle: ${handle}`);
        console.log(`Connection type: ${Array.isArray(connections) ? 'Array (new format)' : 'Object (legacy format)'}`);
        
        if (Array.isArray(connections)) {
          // New array format - multiple tools per handle
          processedToolConnections[handle] = connections;
          console.log(`✅ Handle ${handle}: ${connections.length} tool connections (already array format)`);
        } else {
          // Legacy single connection format - convert to array
          processedToolConnections[handle] = [connections];
          console.log(`🔄 Handle ${handle}: 1 tool connection (converted from legacy format)`);
          console.log(`   Converted: ${connections.node_label} (${connections.node_type})`);
        }
      });
      
      console.log("\nProcessed tool_connections:", JSON.stringify(processedToolConnections, null, 2));
      
      // Store processed tool connection data
      const toolConnectionsKey = `${agenticNode.id}_tool_connections`;
      collectedParameters[toolConnectionsKey] = {
        node_id: agenticNode.id,
        node_name: agenticNode.data.label || "AgenticAI Node",
        input_name: "tool_connections",
        value: processedToolConnections,
        connected_to_start: true,
        connected_as_tool: false,
        is_tool_connection_data: true,
      };
    }
  });
  
  return collectedParameters;
}

// Test legacy format conversion
function testLegacyFormatConversion() {
  console.log("🧪 Testing Legacy Format Conversion");
  console.log("=" .repeat(60));
  
  console.log("📋 Testing with LEGACY format data:");
  const legacyResult = simulateConversionLogic(mockLegacyNodes);
  
  console.log("\n📋 Testing with NEW format data:");
  const newResult = simulateConversionLogic(mockNewNodes);
  
  // Verify conversion results
  console.log("\n✅ Verification Results:");
  
  const legacyProcessed = legacyResult["agentic-ai-1_tool_connections"]?.value;
  const newProcessed = newResult["agentic-ai-1_tool_connections"]?.value;
  
  // Check if legacy format was converted to arrays
  const legacyTool1IsArray = Array.isArray(legacyProcessed?.tool_1);
  const legacyTool2IsArray = Array.isArray(legacyProcessed?.tool_2);
  const legacyTool1Length = legacyProcessed?.tool_1?.length;
  const legacyTool2Length = legacyProcessed?.tool_2?.length;
  
  // Check if new format remains as arrays
  const newTool1IsArray = Array.isArray(newProcessed?.tool_1);
  const newTool2IsArray = Array.isArray(newProcessed?.tool_2);
  const newTool1Length = newProcessed?.tool_1?.length;
  const newTool2Length = newProcessed?.tool_2?.length;
  
  console.log("\nLegacy Format Conversion Results:");
  console.log(`tool_1 converted to array: ${legacyTool1IsArray ? '✅' : '❌'} (length: ${legacyTool1Length})`);
  console.log(`tool_2 converted to array: ${legacyTool2IsArray ? '✅' : '❌'} (length: ${legacyTool2Length})`);
  
  console.log("\nNew Format Preservation Results:");
  console.log(`tool_1 remains array: ${newTool1IsArray ? '✅' : '❌'} (length: ${newTool1Length})`);
  console.log(`tool_2 remains array: ${newTool2IsArray ? '✅' : '❌'} (length: ${newTool2Length})`);
  
  // Check if the converted data has the same content
  const legacyTool1Data = legacyProcessed?.tool_1?.[0];
  const newTool1Data = newProcessed?.tool_1?.[0];
  
  const dataMatches = legacyTool1Data?.node_id === newTool1Data?.node_id &&
                     legacyTool1Data?.node_label === newTool1Data?.node_label;
  
  console.log(`\nData integrity preserved: ${dataMatches ? '✅' : '❌'}`);
  
  if (dataMatches) {
    console.log(`  Legacy: ${legacyTool1Data?.node_label} (${legacyTool1Data?.node_type})`);
    console.log(`  New:    ${newTool1Data?.node_label} (${newTool1Data?.node_type})`);
  }
  
  return {
    legacyConvertedCorrectly: legacyTool1IsArray && legacyTool2IsArray && legacyTool1Length === 1 && legacyTool2Length === 1,
    newFormatPreserved: newTool1IsArray && newTool2IsArray && newTool1Length === 1 && newTool2Length === 1,
    dataIntegrityPreserved: dataMatches,
    legacyResult,
    newResult
  };
}

// Test mixed format scenario
function testMixedFormatScenario() {
  console.log("\n🔀 Testing Mixed Format Scenario");
  console.log("=" .repeat(50));
  
  // Create a mixed scenario - some handles have legacy format, others have new format
  const mixedFormatNode = {
    id: "agentic-ai-mixed",
    data: {
      originalType: "AgenticAI",
      label: "Mixed Format AI Agent",
      config: { 
        num_tool_handles: 3,
        tool_connections: {
          // Legacy format
          "tool_1": {
            node_id: "legacy-component",
            node_type: "LegacyComponent",
            node_label: "Legacy Tool",
            component_definition: { name: "LegacyComponent" }
          },
          // New format
          "tool_2": [
            {
              node_id: "new-component-1",
              node_type: "NewComponent",
              node_label: "New Tool 1",
              component_definition: { name: "NewComponent" }
            },
            {
              node_id: "new-component-2", 
              node_type: "NewComponent",
              node_label: "New Tool 2",
              component_definition: { name: "NewComponent" }
            }
          ],
          // Another legacy format
          "tool_3": {
            node_id: "another-legacy",
            node_type: "AnotherLegacyComponent", 
            node_label: "Another Legacy Tool",
            component_definition: { name: "AnotherLegacyComponent" }
          }
        }
      }
    }
  };
  
  const mixedResult = simulateConversionLogic([mixedFormatNode]);
  const processedConnections = mixedResult["agentic-ai-mixed_tool_connections"]?.value;
  
  console.log("\n✅ Mixed Format Verification:");
  console.log(`tool_1 (legacy): ${Array.isArray(processedConnections?.tool_1) ? '✅ Converted to array' : '❌ Not converted'} (length: ${processedConnections?.tool_1?.length})`);
  console.log(`tool_2 (new): ${Array.isArray(processedConnections?.tool_2) ? '✅ Remains array' : '❌ Not array'} (length: ${processedConnections?.tool_2?.length})`);
  console.log(`tool_3 (legacy): ${Array.isArray(processedConnections?.tool_3) ? '✅ Converted to array' : '❌ Not converted'} (length: ${processedConnections?.tool_3?.length})`);
  
  const allCorrect = Array.isArray(processedConnections?.tool_1) && 
                    Array.isArray(processedConnections?.tool_2) && 
                    Array.isArray(processedConnections?.tool_3) &&
                    processedConnections?.tool_1?.length === 1 &&
                    processedConnections?.tool_2?.length === 2 &&
                    processedConnections?.tool_3?.length === 1;
  
  return {
    success: allCorrect,
    processedConnections
  };
}

// Run all conversion tests
console.log("🚀 Starting Legacy Format Conversion Tests\n");

const conversionTest = testLegacyFormatConversion();
const mixedTest = testMixedFormatScenario();

console.log("\n📈 Conversion Test Summary:");
console.log("=" .repeat(40));
console.log(`Legacy Conversion: ${conversionTest.legacyConvertedCorrectly ? '✅ PASS' : '❌ FAIL'}`);
console.log(`New Format Preservation: ${conversionTest.newFormatPreserved ? '✅ PASS' : '❌ FAIL'}`);
console.log(`Data Integrity: ${conversionTest.dataIntegrityPreserved ? '✅ PASS' : '❌ FAIL'}`);
console.log(`Mixed Format Handling: ${mixedTest.success ? '✅ PASS' : '❌ FAIL'}`);

const allTestsPass = conversionTest.legacyConvertedCorrectly && 
                    conversionTest.newFormatPreserved && 
                    conversionTest.dataIntegrityPreserved && 
                    mixedTest.success;

console.log(`Overall: ${allTestsPass ? '✅ ALL CONVERSION TESTS PASS' : '❌ SOME CONVERSION TESTS FAILED'}`);

if (allTestsPass) {
  console.log("\n🎉 Legacy format conversion is working correctly!");
  console.log("   - Legacy single-object format → Array format ✅");
  console.log("   - New array format preserved ✅");
  console.log("   - Data integrity maintained ✅");
  console.log("   - Mixed format scenarios handled ✅");
} else {
  console.log("\n⚠️  Some conversion issues detected. Check the logs above for details.");
}