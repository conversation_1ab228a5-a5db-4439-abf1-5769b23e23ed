"""
Tests for orchestration engine tool extraction functionality
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from typing import Dict, Any, List

from app.utils.tool_extractor import ToolExtractor
from app.services.agent_executor import AgentExecutor
from app.core_.transition_handler import TransitionHandler


class TestToolExtractor:
    """Test tool extraction utility functionality"""

    def test_tool_extractor_initialization(self):
        """Test ToolExtractor initialization"""
        extractor = ToolExtractor()
        assert extractor is not None
        assert hasattr(extractor, 'extract_tool_schemas')
        assert hasattr(extractor, 'extract_tools_from_component_data')

    def test_extract_tool_schemas_from_component_data(self):
        """Test extracting tool schemas from component data"""
        extractor = ToolExtractor()
        
        # Mock component data with connected tools
        component_data = {
            "component_id": "agentic-ai-1",
            "component_type": "AgenticAI",
            "component_name": "AI Agent",
            "tools": [
                {
                    "tool_type": "workflow_component",
                    "component": {
                        "component_id": "data-processor-1",
                        "component_type": "DataProcessor",
                        "component_name": "Data Processing Tool",
                        "component_schema": {
                            "name": "data_processing_tool",
                            "description": "Process data efficiently",
                            "parameters": {
                                "type": "object",
                                "properties": {
                                    "input_data": {
                                        "type": "string",
                                        "description": "Data to process"
                                    }
                                },
                                "required": ["input_data"],
                                "additionalProperties": False
                            },
                            "strict": False
                        }
                    }
                }
            ]
        }
        
        extracted_tools = extractor.extract_tools_from_component_data(component_data)
        
        # Verify extraction
        assert len(extracted_tools) == 1
        tool = extracted_tools[0]
        assert tool["tool_type"] == "workflow_component"
        assert "component" in tool
        assert "component_schema" in tool["component"]
        
        # Verify schema structure
        schema = tool["component"]["component_schema"]
        assert schema["name"] == "data_processing_tool"
        assert "parameters" in schema
        assert "input_data" in schema["parameters"]["properties"]

    def test_extract_tool_schemas_performance(self):
        """Test tool extraction performance meets requirements"""
        import time
        
        extractor = ToolExtractor()
        
        # Create component data with 10 tools
        tools = []
        for i in range(10):
            tools.append({
                "tool_type": "workflow_component",
                "component": {
                    "component_id": f"component-{i}",
                    "component_type": "TestComponent",
                    "component_name": f"Test Component {i}",
                    "component_schema": {
                        "name": f"test_component_{i}",
                        "description": f"Test component {i}",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "param1": {"type": "string"},
                                "param2": {"type": "integer"}
                            },
                            "required": ["param1"],
                            "additionalProperties": False
                        },
                        "strict": False
                    }
                }
            })
        
        component_data = {
            "component_id": "agentic-ai-1",
            "component_type": "AgenticAI",
            "tools": tools
        }
        
        # Measure extraction time
        start_time = time.time()
        extracted_tools = extractor.extract_tools_from_component_data(component_data)
        end_time = time.time()
        
        extraction_time = (end_time - start_time) * 1000  # Convert to milliseconds
        
        # Should extract tools in less than 100ms
        assert extraction_time < 100, f"Tool extraction took {extraction_time:.2f}ms, should be < 100ms"
        
        # Verify all tools were extracted
        assert len(extracted_tools) == 10

    def test_extract_tool_schemas_error_handling(self):
        """Test error handling for malformed component data"""
        extractor = ToolExtractor()
        
        # Test with missing tools field
        component_data_no_tools = {
            "component_id": "agentic-ai-1",
            "component_type": "AgenticAI"
        }
        
        extracted_tools = extractor.extract_tools_from_component_data(component_data_no_tools)
        assert extracted_tools == []
        
        # Test with malformed tool data
        component_data_malformed = {
            "component_id": "agentic-ai-1",
            "component_type": "AgenticAI",
            "tools": [
                {
                    "tool_type": "workflow_component",
                    "component": {
                        "component_id": "malformed-component"
                        # Missing required fields
                    }
                }
            ]
        }
        
        # Should handle gracefully and skip malformed tools
        extracted_tools = extractor.extract_tools_from_component_data(component_data_malformed)
        assert len(extracted_tools) == 0

    def test_extract_mcp_component_tools(self):
        """Test extracting MCP marketplace component tools"""
        extractor = ToolExtractor()
        
        component_data = {
            "component_id": "agentic-ai-1",
            "component_type": "AgenticAI",
            "tools": [
                {
                    "tool_type": "workflow_component",
                    "component": {
                        "component_id": "mcp-translate-1",
                        "component_type": "MCPMarketplace",
                        "component_name": "Translation Tool",
                        "component_schema": {
                            "name": "translate",
                            "description": "Translate text between languages",
                            "parameters": {
                                "type": "object",
                                "properties": {
                                    "text": {"type": "string"},
                                    "target_language": {"type": "string"}
                                },
                                "required": ["text", "target_language"],
                                "additionalProperties": False
                            },
                            "strict": False
                        },
                        "mcp_metadata": {
                            "server_url": "http://localhost:8000",
                            "tool_name": "translate"
                        }
                    }
                }
            ]
        }
        
        extracted_tools = extractor.extract_tools_from_component_data(component_data)
        
        # Verify MCP tool extraction
        assert len(extracted_tools) == 1
        tool = extracted_tools[0]
        assert tool["component"]["component_type"] == "MCPMarketplace"
        assert "mcp_metadata" in tool["component"]
        assert tool["component"]["mcp_metadata"]["server_url"] == "http://localhost:8000"


class TestAgentExecutorToolIntegration:
    """Test agent executor integration with tool extraction"""

    @pytest.mark.asyncio
    async def test_agent_executor_includes_tool_schemas(self):
        """Test that agent executor includes tool schemas in Kafka messages"""
        # Mock Kafka producer
        mock_producer = AsyncMock()

        agent_executor = AgentExecutor(mock_producer)

        # Mock the consumer and consumer task to bypass the runtime check
        agent_executor._consumer = Mock()
        agent_executor._consumer_task = Mock()
        agent_executor._consumer_task.done.return_value = False
        
        # Mock tool parameters with agent config containing tools
        tool_parameters = {
            "agent_type": "component",
            "execution_type": "response",
            "query": "Process the data",
            "agent_config": {
                "id": "test-agent-1",
                "name": "Test Agent",
                "description": "Test agent with tools",
                "system_message": "You are a test agent",
                "model_config": {
                    "model_provider": "openai",
                    "model": "gpt-4",
                    "temperature": 0.7,
                    "max_tokens": 1000
                },
                "tools": [
                    {
                        "tool_type": "workflow_component",
                        "component": {
                            "component_id": "data-processor-1",
                            "component_type": "DataProcessor",
                            "component_name": "Data Processing Tool",
                            "component_schema": {
                                "name": "data_processing_tool",
                                "description": "Process data",
                                "parameters": {
                                    "type": "object",
                                    "properties": {
                                        "input_data": {"type": "string"}
                                    },
                                    "required": ["input_data"],
                                    "additionalProperties": False
                                },
                                "strict": False
                            }
                        }
                    }
                ]
            }
        }
        
        # Mock the Kafka message sending and future handling
        with patch.object(agent_executor, '_send_kafka_message', new_callable=AsyncMock) as mock_send:
            # Create a real future and set its result
            mock_future = asyncio.Future()
            mock_future.set_result({"status": "success", "result": "Agent executed successfully"})

            with patch('asyncio.Future', return_value=mock_future):
                # Execute the tool
                result = await agent_executor.execute_tool(
                    server_script_path=None,
                    tool_name="test_agent",
                    tool_parameters=tool_parameters
                )
            
                # Verify Kafka message was sent
                assert mock_send.called

                # Get the sent message
                call_args = mock_send.call_args
                sent_message = call_args[0][1]  # Second argument is the message

                # Verify agent_config.tools is included in the message
                assert "agent_config" in sent_message
                assert "tools" in sent_message["agent_config"]
                assert len(sent_message["agent_config"]["tools"]) == 1

                # Verify tool schema is preserved
                tool = sent_message["agent_config"]["tools"][0]
                assert tool["tool_type"] == "workflow_component"
                assert "component_schema" in tool["component"]
                assert tool["component"]["component_schema"]["name"] == "data_processing_tool"

    @pytest.mark.asyncio
    async def test_agent_executor_handles_empty_tools(self):
        """Test agent executor handles components with no tools"""
        mock_producer = AsyncMock()
        agent_executor = AgentExecutor(mock_producer)

        # Mock the consumer and consumer task to bypass the runtime check
        agent_executor._consumer = Mock()
        agent_executor._consumer_task = Mock()
        agent_executor._consumer_task.done.return_value = False
        
        tool_parameters = {
            "agent_type": "component",
            "execution_type": "response",
            "query": "Simple query",
            "agent_config": {
                "id": "test-agent-2",
                "name": "Simple Agent",
                "description": "Agent without tools",
                "system_message": "You are a simple agent",
                "model_config": {
                    "model_provider": "openai",
                    "model": "gpt-4",
                    "temperature": 0.7,
                    "max_tokens": 1000
                },
                "tools": []  # Empty tools array
            }
        }
        
        with patch.object(agent_executor, '_send_kafka_message', new_callable=AsyncMock) as mock_send:
            # Create a real future and set its result
            mock_future = asyncio.Future()
            mock_future.set_result({"status": "success", "result": "Agent executed successfully"})

            with patch('asyncio.Future', return_value=mock_future):
                result = await agent_executor.execute_tool(
                    server_script_path=None,
                    tool_name="simple_agent",
                    tool_parameters=tool_parameters
                )

                # Verify message was sent with empty tools array
                call_args = mock_send.call_args
                sent_message = call_args[0][1]

                assert "agent_config" in sent_message
                assert "tools" in sent_message["agent_config"]
                assert sent_message["agent_config"]["tools"] == []


class TestTransitionHandlerToolExtraction:
    """Test transition handler integration with tool extraction"""

    @pytest.mark.asyncio
    async def test_transition_handler_extracts_tools_for_agentic_ai(self):
        """Test that transition handler extracts tools for AgenticAI components"""
        # Mock dependencies
        mock_state_manager = Mock()
        mock_workflow_utils = Mock()
        mock_tool_executor = Mock()
        mock_agent_executor = AsyncMock()
        
        # Create transition handler
        transition_handler = TransitionHandler(
            state_manager=mock_state_manager,
            transitions_by_id={},
            nodes={},
            dependency_map={},
            workflow_utils=mock_workflow_utils,
            tool_executor=mock_tool_executor,
            agent_executor=mock_agent_executor
        )
        
        # Mock transition with AgenticAI component
        transition = {
            "id": "transition-1",
            "transition_type": "standard",
            "execution_type": "Agents",
            "node_info": {
                "node_id": "agentic-ai-1",
                "tools_to_use": [
                    {
                        "tool_id": 1,
                        "tool_name": "agentic_ai_executor",
                        "tool_params": {"items": []}
                    }
                ]
            }
        }
        
        # Mock node details
        node_details = {
            "server_script_path": "",
            "server_tools": [
                {
                    "tool_id": 1,
                    "tool_name": "agentic_ai_executor",
                    "input_schema": {
                        "predefined_fields": [
                            {
                                "field_name": "agent_config",
                                "data_type": {"type": "object"},
                                "required": True
                            }
                        ]
                    }
                }
            ]
        }
        
        # Mock nodes dictionary
        transition_handler.nodes = {"agentic-ai-1": node_details}
        
        # Mock state manager methods
        mock_state_manager.mark_transition_completed = Mock()
        mock_state_manager.get_data_for_handle = Mock(return_value={
            "agent_config": {
                "id": "test-agent",
                "name": "Test Agent",
                "tools": [
                    {
                        "tool_type": "workflow_component",
                        "component": {
                            "component_id": "data-processor-1",
                            "component_type": "DataProcessor",
                            "component_name": "Data Processing Tool",
                            "component_schema": {
                                "name": "data_processing_tool",
                                "description": "Process data",
                                "parameters": {
                                    "type": "object",
                                    "properties": {
                                        "input_data": {"type": "string"}
                                    },
                                    "required": ["input_data"],
                                    "additionalProperties": False
                                },
                                "strict": False
                            }
                        }
                    }
                ]
            }
        })
        
        # Mock agent executor
        mock_agent_executor.execute_tool.return_value = {
            "status": "success",
            "result": "Agent executed with tools"
        }
        
        # Mock result callback
        mock_result_callback = AsyncMock()
        transition_handler.result_callback = mock_result_callback
        
        # Execute transition
        result = await transition_handler._execute_standard_or_reflection_transition(transition)
        
        # Verify agent executor was called with tool schemas
        assert mock_agent_executor.execute_tool.called
        call_args = mock_agent_executor.execute_tool.call_args
        tool_parameters = call_args[1]['tool_parameters']  # keyword argument
        
        # Verify agent_config contains tools
        assert "agent_config" in tool_parameters
        assert "tools" in tool_parameters["agent_config"]
        assert len(tool_parameters["agent_config"]["tools"]) == 1
        
        # Verify tool schema is included
        tool = tool_parameters["agent_config"]["tools"][0]
        assert "component_schema" in tool["component"]
        assert tool["component"]["component_schema"]["name"] == "data_processing_tool"
