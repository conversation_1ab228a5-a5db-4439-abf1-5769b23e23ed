# How Tools Become Visible: Technical Implementation

## 🔧 Current Problem: Tools Are Hidden

Looking at `agentic_schema.json`, the tools exist only in config:

```json
{
  "workflow_data": {
    "nodes": [
      {"id": "start-node", ...},
      {
        "id": "AgenticAI-1749976462114",
        "data": {
          "config": {
            "tool_connections": {
              "tool_1": [
                {"node_id": "tool-candidate-suitability", "component_definition": {...}},
                {"node_id": "tool-tavily-crawl", "component_definition": {...}},
                {"node_id": "tool-calendar-update", "component_definition": {...}},
                {"node_id": "tool-fetch", "component_definition": {...}}
              ]
            }
          }
        }
      }
    ]
  }
}
```

**Problem**: Tools exist only as data inside AgenticAI config, not as actual workflow nodes.

## 🚀 Solution: Convert Config Data to Actual Nodes

### Step 1: Extract Tool Data from Config
```typescript
function extractToolsFromConfig(agenticAINode) {
  const toolConnections = agenticAINode.data.config.tool_connections;
  const toolNodes = [];
  
  // Loop through tool_1, tool_2, etc.
  Object.entries(toolConnections).forEach(([handle, tools]) => {
    tools.forEach((toolData, index) => {
      // Create actual workflow node from config data
      const toolNode = {
        id: toolData.node_id,
        type: "WorkflowNode",
        position: { x: 400 + (index * 150), y: 200 }, // Auto-position
        data: {
          label: toolData.node_label,
          originalType: toolData.node_type,
          definition: toolData.component_definition,
          type: "tool"
        }
      };
      toolNodes.push(toolNode);
    });
  });
  
  return toolNodes;
}
```

### Step 2: Create Visual Edges
```typescript
function createToolEdges(agenticAINodeId, toolNodes) {
  const edges = [];
  
  toolNodes.forEach(toolNode => {
    // Create edge from tool to AgenticAI
    const edge = {
      id: `edge-${toolNode.id}-${agenticAINodeId}`,
      source: toolNode.id,
      target: agenticAINodeId,
      targetHandle: "tools",
      type: "default"
    };
    edges.push(edge);
  });
  
  return edges;
}
```

### Step 3: Update Workflow Data
```typescript
function makeToolsVisible(workflowData) {
  const { nodes, edges } = workflowData;
  
  // Find AgenticAI node
  const agenticAINode = nodes.find(n => n.data.originalType === "AgenticAI");
  
  if (agenticAINode && agenticAINode.data.config.tool_connections) {
    // Extract tools from config
    const toolNodes = extractToolsFromConfig(agenticAINode);
    
    // Create edges for tools
    const toolEdges = createToolEdges(agenticAINode.id, toolNodes);
    
    // Add tool nodes to workflow
    const updatedNodes = [...nodes, ...toolNodes];
    const updatedEdges = [...edges, ...toolEdges];
    
    // Clear tool_connections from config (now redundant)
    agenticAINode.data.config.tool_connections = {};
    
    return {
      nodes: updatedNodes,
      edges: updatedEdges
    };
  }
  
  return workflowData;
}
```

## 🎨 Frontend Implementation

### When Loading Existing Workflow
```typescript
// In workflow loader component
function loadWorkflow(savedWorkflowData) {
  // Check if workflow has hidden tools in config
  const hasHiddenTools = savedWorkflowData.nodes.some(node => 
    node.data.originalType === "AgenticAI" && 
    node.data.config.tool_connections &&
    Object.keys(node.data.config.tool_connections).length > 0
  );
  
  if (hasHiddenTools) {
    // Convert hidden tools to visible nodes
    const updatedWorkflow = makeToolsVisible(savedWorkflowData);
    setNodes(updatedWorkflow.nodes);
    setEdges(updatedWorkflow.edges);
  } else {
    // Normal workflow loading
    setNodes(savedWorkflowData.nodes);
    setEdges(savedWorkflowData.edges);
  }
}
```

### Tool Node Component
```tsx
const ToolNode = ({ data }) => {
  return (
    <div className="tool-node">
      <div className="tool-header">
        <Icon name="Cloud" className="mcp-icon" />
        <span className="tool-type">MCP</span>
      </div>
      <div className="tool-title">{data.label}</div>
      <div className="tool-description">
        {data.definition?.description || "MCP Tool"}
      </div>
      <Handle 
        type="source" 
        position={Position.Right} 
        id="output"
      />
    </div>
  );
};
```

## 📋 Step-by-Step Process

### For Your Current agentic_schema.json:

1. **Detect Hidden Tools**
   ```typescript
   // Found: 4 tools in AgenticAI-1749976462114.data.config.tool_connections.tool_1
   ```

2. **Create Tool Nodes**
   ```typescript
   const toolNodes = [
     {
       id: "MCP_Candidate_Interview_candidate_suitability-1749976479412",
       type: "WorkflowNode",
       position: { x: 400, y: 200 },
       data: { label: "Candidate Interview - candidate_suitability", ... }
     },
     {
       id: "MCP_Tavily_Web_Search_and_Extraction_Server_tavily-crawl-1749976522030", 
       type: "WorkflowNode",
       position: { x: 550, y: 200 },
       data: { label: "Tavily Web Search - tavily-crawl", ... }
     },
     // ... 2 more tools
   ];
   ```

3. **Create Edges**
   ```typescript
   const toolEdges = [
     { source: "tool-candidate-suitability", target: "AgenticAI-1749976462114", targetHandle: "tools" },
     { source: "tool-tavily-crawl", target: "AgenticAI-1749976462114", targetHandle: "tools" },
     // ... 2 more edges
   ];
   ```

4. **Update Canvas**
   ```typescript
   // Before: 2 nodes, 1 edge
   // After: 6 nodes, 5 edges
   ```

## 🔄 Migration Options

### Option 1: Automatic Conversion on Load
```typescript
// Every time user opens workflow, automatically convert hidden tools to visible
```

### Option 2: Manual Migration Button
```tsx
<button onClick={() => convertHiddenToolsToVisible()}>
  Show Hidden Tools on Canvas
</button>
```

### Option 3: Gradual Migration
```typescript
// New workflows: Always use visible tools
// Old workflows: Support both formats, offer conversion
```

The key is that we're taking the tool data that currently exists only as JSON config and creating actual React Flow nodes from it, making them visible and interactive on the canvas.