"""
Component Executor Utility - Enhanced component execution with tool support.
"""

import asyncio
import json
import time
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass

from app.core_.component_system import get_component_manager
from app.core_.tool_executor import get_tool_executor
from app.utils.logging_config import setup_logger

logger = setup_logger("ComponentExecutor")


@dataclass
class ComponentExecutionResult:
    """
    Result of component execution.
    """
    success: bool
    result: Any
    execution_time: float
    error: Optional[str] = None
    component_type: Optional[str] = None
    request_id: Optional[str] = None


class ComponentExecutor:
    """
    Enhanced component executor with tool component support.
    
    This utility provides advanced component execution capabilities including:
    - Tool component execution
    - Batch execution
    - Performance monitoring
    - Error handling and recovery
    """
    
    def __init__(self):
        """Initialize the component executor."""
        self.component_manager = get_component_manager()
        self.tool_executor = get_tool_executor()
        self.execution_history: List[ComponentExecutionResult] = []
        self.performance_metrics = {
            "total_executions": 0,
            "successful_executions": 0,
            "failed_executions": 0,
            "average_execution_time": 0.0,
            "fastest_execution": float('inf'),
            "slowest_execution": 0.0
        }
        
        logger.info("ComponentExecutor initialized")

    async def execute_component(
        self, 
        component_type: str, 
        payload: Dict[str, Any],
        timeout: float = 30.0,
        retry_count: int = 0
    ) -> ComponentExecutionResult:
        """
        Execute a component with enhanced error handling and monitoring.
        
        Args:
            component_type: The component type to execute
            payload: The execution payload
            timeout: Execution timeout in seconds
            retry_count: Number of retries on failure
            
        Returns:
            ComponentExecutionResult with execution details
        """
        start_time = time.time()
        request_id = payload.get("request_id", f"comp-exec-{int(start_time)}")
        
        logger.info(f"Executing component {component_type} for request {request_id}")
        
        for attempt in range(retry_count + 1):
            try:
                if attempt > 0:
                    logger.info(f"Retry attempt {attempt} for component {component_type}")
                
                # Execute with timeout
                result = await asyncio.wait_for(
                    self._execute_component_internal(component_type, payload),
                    timeout=timeout
                )
                
                execution_time = time.time() - start_time
                
                # Create successful result
                exec_result = ComponentExecutionResult(
                    success=True,
                    result=result,
                    execution_time=execution_time,
                    component_type=component_type,
                    request_id=request_id
                )
                
                # Update metrics and history
                self._update_metrics(exec_result)
                self.execution_history.append(exec_result)
                
                logger.info(f"Component {component_type} executed successfully in {execution_time:.2f}s")
                return exec_result
                
            except asyncio.TimeoutError:
                error_msg = f"Component {component_type} execution timed out after {timeout}s"
                logger.error(error_msg)
                
                if attempt == retry_count:  # Last attempt
                    execution_time = time.time() - start_time
                    exec_result = ComponentExecutionResult(
                        success=False,
                        result=None,
                        execution_time=execution_time,
                        error=error_msg,
                        component_type=component_type,
                        request_id=request_id
                    )
                    self._update_metrics(exec_result)
                    self.execution_history.append(exec_result)
                    return exec_result
                    
            except Exception as e:
                error_msg = f"Error executing component {component_type}: {str(e)}"
                logger.error(error_msg)
                
                if attempt == retry_count:  # Last attempt
                    execution_time = time.time() - start_time
                    exec_result = ComponentExecutionResult(
                        success=False,
                        result=None,
                        execution_time=execution_time,
                        error=error_msg,
                        component_type=component_type,
                        request_id=request_id
                    )
                    self._update_metrics(exec_result)
                    self.execution_history.append(exec_result)
                    return exec_result
                
                # Wait before retry
                await asyncio.sleep(0.5 * (attempt + 1))
        
        # Should not reach here, but just in case
        execution_time = time.time() - start_time
        exec_result = ComponentExecutionResult(
            success=False,
            result=None,
            execution_time=execution_time,
            error="Unknown execution error",
            component_type=component_type,
            request_id=request_id
        )
        self._update_metrics(exec_result)
        self.execution_history.append(exec_result)
        return exec_result

    async def execute_batch(
        self, 
        executions: List[Dict[str, Any]],
        max_concurrent: int = 5
    ) -> List[ComponentExecutionResult]:
        """
        Execute multiple components concurrently.
        
        Args:
            executions: List of execution configurations
            max_concurrent: Maximum concurrent executions
            
        Returns:
            List of execution results
        """
        logger.info(f"Starting batch execution of {len(executions)} components")
        
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def execute_with_semaphore(exec_config):
            async with semaphore:
                return await self.execute_component(
                    component_type=exec_config["component_type"],
                    payload=exec_config["payload"],
                    timeout=exec_config.get("timeout", 30.0),
                    retry_count=exec_config.get("retry_count", 0)
                )
        
        # Execute all components concurrently
        tasks = [execute_with_semaphore(config) for config in executions]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Convert exceptions to failed results
        final_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                exec_config = executions[i]
                final_results.append(ComponentExecutionResult(
                    success=False,
                    result=None,
                    execution_time=0.0,
                    error=str(result),
                    component_type=exec_config["component_type"],
                    request_id=exec_config["payload"].get("request_id", f"batch-{i}")
                ))
            else:
                final_results.append(result)
        
        successful = sum(1 for r in final_results if r.success)
        logger.info(f"Batch execution completed: {successful}/{len(final_results)} successful")
        
        return final_results

    async def _execute_component_internal(self, component_type: str, payload: Dict[str, Any]) -> Any:
        """
        Internal component execution method.
        
        Args:
            component_type: The component type to execute
            payload: The execution payload
            
        Returns:
            Component execution result
        """
        # Check if it's a tool component
        tool_status = self.component_manager.get_tool_component_status(component_type)
        
        if tool_status.get("is_tool_component", False):
            logger.debug(f"Executing as tool component: {component_type}")
            # Use tool executor for tool components
            return await self.tool_executor.execute_tool({
                "tool_name": component_type,
                "tool_parameters": payload,
                "request_id": payload.get("request_id")
            })
        else:
            logger.debug(f"Executing as regular component: {component_type}")
            # Use component manager for regular components
            component = self.component_manager.get_component_instance(component_type)
            return await component.process(payload)

    def _update_metrics(self, result: ComponentExecutionResult) -> None:
        """
        Update performance metrics.
        
        Args:
            result: The execution result to update metrics with
        """
        self.performance_metrics["total_executions"] += 1
        
        if result.success:
            self.performance_metrics["successful_executions"] += 1
        else:
            self.performance_metrics["failed_executions"] += 1
        
        # Update timing metrics
        exec_time = result.execution_time
        if exec_time < self.performance_metrics["fastest_execution"]:
            self.performance_metrics["fastest_execution"] = exec_time
        if exec_time > self.performance_metrics["slowest_execution"]:
            self.performance_metrics["slowest_execution"] = exec_time
        
        # Update average
        total = self.performance_metrics["total_executions"]
        current_avg = self.performance_metrics["average_execution_time"]
        self.performance_metrics["average_execution_time"] = (
            (current_avg * (total - 1) + exec_time) / total
        )

    def get_performance_metrics(self) -> Dict[str, Any]:
        """
        Get performance metrics.
        
        Returns:
            Dictionary containing performance metrics
        """
        return self.performance_metrics.copy()

    def get_execution_history(self, limit: int = 100) -> List[ComponentExecutionResult]:
        """
        Get execution history.
        
        Args:
            limit: Maximum number of results to return
            
        Returns:
            List of recent execution results
        """
        return self.execution_history[-limit:]

    def clear_history(self) -> None:
        """Clear execution history."""
        self.execution_history.clear()
        logger.info("Execution history cleared")

    def reset_metrics(self) -> None:
        """Reset performance metrics."""
        self.performance_metrics = {
            "total_executions": 0,
            "successful_executions": 0,
            "failed_executions": 0,
            "average_execution_time": 0.0,
            "fastest_execution": float('inf'),
            "slowest_execution": 0.0
        }
        logger.info("Performance metrics reset")


# Global component executor instance
_component_executor = None


def get_component_executor() -> ComponentExecutor:
    """
    Get or create the global component executor instance.
    
    Returns:
        The component executor instance
    """
    global _component_executor
    if _component_executor is None:
        logger.info("Creating new global ComponentExecutor instance")
        _component_executor = ComponentExecutor()
    return _component_executor
