"""
Execution Service - Handles tool-connected component execution preparation and management.
"""

import asyncio
import logging
import time
from typing import Dict, Any, List, Optional
from dataclasses import dataclass

from app.core_.component_system import get_component_manager, ComponentManager
from app.core_.tool_executor import get_tool_executor, ToolExecutor
from app.utils.logging_config import setup_logger

logger = setup_logger("ExecutionService")


@dataclass
class ExecutionContext:
    """
    Execution context for tool component execution.
    """
    request_id: str
    correlation_id: Optional[str] = None
    agent_id: Optional[str] = None
    execution_type: str = "tool_execution"
    tool_metadata: Optional[Dict[str, Any]] = None
    start_time: Optional[float] = None
    timeout: float = 30.0  # Default 30 second timeout


class ExecutionService:
    """
    Service for managing tool-connected component execution.
    
    This service prepares tool components for execution, manages execution context,
    and ensures components are available when agents call them.
    """
    
    def __init__(self, component_manager: ComponentManager = None, tool_executor: ToolExecutor = None):
        """
        Initialize the execution service.
        
        Args:
            component_manager: Optional component manager instance
            tool_executor: Optional tool executor instance
        """
        self.component_manager = component_manager or get_component_manager()
        self.tool_executor = tool_executor or get_tool_executor()
        self.active_executions: Dict[str, ExecutionContext] = {}
        self.execution_stats = {
            "total_executions": 0,
            "successful_executions": 0,
            "failed_executions": 0,
            "average_execution_time": 0.0
        }
        
        logger.info("ExecutionService initialized")

    async def prepare_tool_component(self, component_config: Dict[str, Any]) -> bool:
        """
        Prepare a tool component for execution.
        
        Args:
            component_config: Component configuration with tool metadata
            
        Returns:
            True if preparation was successful, False otherwise
        """
        try:
            component_type = component_config.get("component_type")
            component_id = component_config.get("component_id")
            
            logger.info(f"Preparing tool component: {component_type} (ID: {component_id})")
            
            # Register the tool component
            success = self.component_manager.register_tool_component(component_config)
            
            if success:
                # Verify component is available
                is_available = self.component_manager.is_component_available(component_type)
                if is_available:
                    logger.info(f"Tool component {component_type} prepared and available")
                    return True
                else:
                    logger.error(f"Tool component {component_type} registered but not available")
                    return False
            else:
                logger.error(f"Failed to register tool component {component_type}")
                return False
                
        except Exception as e:
            logger.error(f"Error preparing tool component: {str(e)}")
            return False

    async def execute_tool_component(
        self, 
        component_type: str, 
        payload: Dict[str, Any], 
        execution_context: ExecutionContext = None
    ) -> Dict[str, Any]:
        """
        Execute a tool component with proper context management.
        
        Args:
            component_type: The component type to execute
            payload: The execution payload
            execution_context: Optional execution context
            
        Returns:
            Execution result dictionary
        """
        # Create execution context if not provided
        if execution_context is None:
            execution_context = ExecutionContext(
                request_id=payload.get("request_id", f"exec-{int(time.time())}")
            )
        
        execution_context.start_time = time.time()
        
        # Track active execution
        self.active_executions[execution_context.request_id] = execution_context
        
        try:
            logger.info(f"Executing tool component {component_type} for request {execution_context.request_id}")
            
            # Update payload with execution context
            enhanced_payload = {
                **payload,
                "execution_context": {
                    "request_id": execution_context.request_id,
                    "correlation_id": execution_context.correlation_id,
                    "agent_id": execution_context.agent_id,
                    "execution_type": execution_context.execution_type
                }
            }
            
            # Execute with timeout
            result = await asyncio.wait_for(
                self.tool_executor.execute_tool(enhanced_payload),
                timeout=execution_context.timeout
            )
            
            # Update statistics
            execution_time = time.time() - execution_context.start_time
            self._update_execution_stats(True, execution_time)
            
            logger.info(f"Tool component {component_type} executed successfully in {execution_time:.2f}s")
            
            return {
                "status": "success",
                "result": result,
                "execution_time": execution_time,
                "request_id": execution_context.request_id
            }
            
        except asyncio.TimeoutError:
            error_msg = f"Tool component {component_type} execution timed out after {execution_context.timeout}s"
            logger.error(error_msg)
            self._update_execution_stats(False, execution_context.timeout)
            
            return {
                "status": "error",
                "error": error_msg,
                "request_id": execution_context.request_id
            }
            
        except Exception as e:
            error_msg = f"Error executing tool component {component_type}: {str(e)}"
            logger.error(error_msg)
            execution_time = time.time() - execution_context.start_time
            self._update_execution_stats(False, execution_time)
            
            return {
                "status": "error",
                "error": error_msg,
                "execution_time": execution_time,
                "request_id": execution_context.request_id
            }
            
        finally:
            # Clean up active execution
            if execution_context.request_id in self.active_executions:
                del self.active_executions[execution_context.request_id]

    def get_tool_component_status(self, component_type: str) -> Dict[str, Any]:
        """
        Get the status of a tool component.
        
        Args:
            component_type: The component type to check
            
        Returns:
            Component status information
        """
        return self.component_manager.get_tool_component_status(component_type)

    def get_active_executions(self) -> Dict[str, ExecutionContext]:
        """
        Get currently active executions.
        
        Returns:
            Dictionary of active execution contexts
        """
        return self.active_executions.copy()

    def get_execution_statistics(self) -> Dict[str, Any]:
        """
        Get execution statistics.
        
        Returns:
            Dictionary containing execution statistics
        """
        return self.execution_stats.copy()

    async def cleanup_stale_executions(self, max_age_seconds: float = 300.0) -> int:
        """
        Clean up stale executions that have been running too long.
        
        Args:
            max_age_seconds: Maximum age in seconds before considering execution stale
            
        Returns:
            Number of stale executions cleaned up
        """
        current_time = time.time()
        stale_executions = []
        
        for request_id, context in self.active_executions.items():
            if context.start_time and (current_time - context.start_time) > max_age_seconds:
                stale_executions.append(request_id)
        
        # Remove stale executions
        for request_id in stale_executions:
            del self.active_executions[request_id]
            logger.warning(f"Cleaned up stale execution: {request_id}")
        
        return len(stale_executions)

    def _update_execution_stats(self, success: bool, execution_time: float) -> None:
        """
        Update execution statistics.
        
        Args:
            success: Whether the execution was successful
            execution_time: Time taken for execution
        """
        self.execution_stats["total_executions"] += 1
        
        if success:
            self.execution_stats["successful_executions"] += 1
        else:
            self.execution_stats["failed_executions"] += 1
        
        # Update average execution time
        total = self.execution_stats["total_executions"]
        current_avg = self.execution_stats["average_execution_time"]
        self.execution_stats["average_execution_time"] = (
            (current_avg * (total - 1) + execution_time) / total
        )


# Global execution service instance
_execution_service = None


def get_execution_service() -> ExecutionService:
    """
    Get or create the global execution service instance.
    
    Returns:
        The execution service instance
    """
    global _execution_service
    if _execution_service is None:
        logger.info("Creating new global ExecutionService instance")
        _execution_service = ExecutionService()
    return _execution_service
