/**
 * Comprehensive test for multiple tools connecting to the same tool handle
 * Tests the new array-based tool connection storage and processing logic
 */

// Mock data for testing multiple tools to same handle
const mockNodes = [
  {
    id: "start-node",
    data: { 
      originalType: "StartNode",
      config: { collected_parameters: {} }
    }
  },
  {
    id: "agentic-ai-1",
    data: {
      originalType: "AgenticAI",
      label: "AI Agent",
      config: { 
        num_tool_handles: 2, 
        tool_connections: {} 
      }
    }
  },
  {
    id: "text-component-1",
    data: { 
      originalType: "TextGeneratorComponent", 
      label: "Text Generator 1",
      definition: { name: "TextGeneratorComponent", type: "component" }
    }
  },
  {
    id: "text-component-2", 
    data: { 
      originalType: "TextGeneratorComponent", 
      label: "Text Generator 2",
      definition: { name: "TextGeneratorComponent", type: "component" }
    }
  },
  {
    id: "data-component-1",
    data: { 
      originalType: "DataProcessorComponent", 
      label: "Data Processor",
      definition: { name: "DataProcessorComponent", type: "component" }
    }
  }
];

// Mock the enhanced onConnect logic that supports multiple tools per handle
function simulateEnhancedOnConnect(connection, nodes) {
  console.log(`\n🔗 Creating connection: ${connection.source} -> ${connection.target} (${connection.targetHandle})`);
  
  // Find the target AgenticAI node
  const targetNode = nodes.find(n => n.id === connection.target);
  if (targetNode && targetNode.data.originalType === "AgenticAI") {
    const sourceNode = nodes.find(n => n.id === connection.source);
    if (sourceNode) {
      // Initialize tool_connections in config if it doesn't exist
      const currentConfig = targetNode.data.config || {};
      const toolConnections = currentConfig.tool_connections || {};
      
      // Support multiple tools per handle - store as array
      if (!toolConnections[connection.targetHandle]) {
        toolConnections[connection.targetHandle] = [];
      }
      
      // Check if this specific node is already connected to this handle
      const existingConnectionIndex = toolConnections[connection.targetHandle].findIndex(
        (conn) => conn.node_id === connection.source
      );
      
      const newToolConnection = {
        node_id: connection.source,
        node_type: sourceNode.data.originalType || sourceNode.data.type,
        node_label: sourceNode.data.label,
        component_definition: sourceNode.data.definition
      };
      
      if (existingConnectionIndex >= 0) {
        // Update existing connection
        toolConnections[connection.targetHandle][existingConnectionIndex] = newToolConnection;
        console.log(`   ✏️  Updated existing connection for ${connection.targetHandle}`);
      } else {
        // Add new connection to the array
        toolConnections[connection.targetHandle].push(newToolConnection);
        console.log(`   ➕ Added new connection to ${connection.targetHandle}. Total: ${toolConnections[connection.targetHandle].length}`);
      }
      
      // Update the node's config
      targetNode.data.config.tool_connections = toolConnections;
      
      return toolConnections;
    }
  }
  return null;
}

// Mock the workflow API processing logic
function simulateWorkflowProcessing(nodes) {
  console.log("\n🔄 Simulating Workflow Processing Logic");
  console.log("=" .repeat(50));
  
  const agenticAINodes = nodes.filter(node => node.data.originalType === "AgenticAI");
  const collectedParameters = {};
  
  agenticAINodes.forEach(agenticNode => {
    if (agenticNode.data.config?.tool_connections) {
      const toolConnections = agenticNode.data.config.tool_connections;
      const processedToolConnections = {};
      
      Object.entries(toolConnections).forEach(([handle, connections]) => {
        if (Array.isArray(connections)) {
          // New array format - multiple tools per handle
          processedToolConnections[handle] = connections;
          console.log(`   Handle ${handle}: ${connections.length} tools connected`);
          connections.forEach((conn, index) => {
            console.log(`     ${index + 1}. ${conn.node_label} (${conn.node_type})`);
          });
        } else {
          // Legacy single connection format - convert to array
          processedToolConnections[handle] = [connections];
          console.log(`   Handle ${handle}: 1 tool connected (legacy format)`);
          console.log(`     1. ${connections.node_label} (${connections.node_type})`);
        }
      });
      
      // Store processed tool connection data
      const toolConnectionsKey = `${agenticNode.id}_tool_connections`;
      collectedParameters[toolConnectionsKey] = {
        node_id: agenticNode.id,
        node_name: agenticNode.data.label || "AgenticAI Node",
        input_name: "tool_connections",
        value: processedToolConnections,
        connected_to_start: true,
        connected_as_tool: false,
        is_tool_connection_data: true,
      };
    }
  });
  
  return collectedParameters;
}

// Test multiple connections to same handle
function testMultipleToolsPerHandle() {
  console.log("🧪 Testing Multiple Tools Per Handle Support");
  console.log("=" .repeat(60));
  
  // Create connections
  const connections = [
    { source: "text-component-1", target: "agentic-ai-1", targetHandle: "tool_1" },
    { source: "text-component-2", target: "agentic-ai-1", targetHandle: "tool_1" }, // Same handle!
    { source: "data-component-1", target: "agentic-ai-1", targetHandle: "tool_2" }
  ];
  
  console.log("Creating connections:");
  connections.forEach((conn, index) => {
    console.log(`${index + 1}. ${conn.source} -> ${conn.targetHandle}`);
  });
  
  // Simulate connections
  let finalToolConnections = null;
  connections.forEach(connection => {
    finalToolConnections = simulateEnhancedOnConnect(connection, mockNodes);
  });
  
  console.log("\n📊 Final Tool Connections State:");
  console.log(JSON.stringify(finalToolConnections, null, 2));
  
  // Test processing logic
  const processedParameters = simulateWorkflowProcessing(mockNodes);
  
  console.log("\n📋 Processed Parameters for Execution:");
  console.log(JSON.stringify(processedParameters, null, 2));
  
  // Verify results
  console.log("\n✅ Verification Results:");
  
  // Check tool_1 has 2 connections
  const tool1Connections = finalToolConnections?.tool_1;
  if (Array.isArray(tool1Connections) && tool1Connections.length === 2) {
    console.log(`✅ tool_1 correctly has ${tool1Connections.length} connections`);
    console.log(`   - ${tool1Connections[0].node_label}`);
    console.log(`   - ${tool1Connections[1].node_label}`);
  } else {
    console.log(`❌ tool_1 should have 2 connections, but has ${tool1Connections?.length || 0}`);
  }
  
  // Check tool_2 has 1 connection
  const tool2Connections = finalToolConnections?.tool_2;
  if (Array.isArray(tool2Connections) && tool2Connections.length === 1) {
    console.log(`✅ tool_2 correctly has ${tool2Connections.length} connection`);
    console.log(`   - ${tool2Connections[0].node_label}`);
  } else {
    console.log(`❌ tool_2 should have 1 connection, but has ${tool2Connections?.length || 0}`);
  }
  
  // Check processing logic handles arrays correctly
  const processedTool1 = processedParameters["agentic-ai-1_tool_connections"]?.value?.tool_1;
  if (Array.isArray(processedTool1) && processedTool1.length === 2) {
    console.log(`✅ Processing logic correctly handles multiple tools for tool_1`);
  } else {
    console.log(`❌ Processing logic failed to handle multiple tools for tool_1`);
  }
  
  return {
    success: tool1Connections?.length === 2 && tool2Connections?.length === 1,
    tool1Count: tool1Connections?.length || 0,
    tool2Count: tool2Connections?.length || 0,
    processedCorrectly: Array.isArray(processedTool1) && processedTool1.length === 2
  };
}

// Test edge removal with multiple connections
function testEdgeRemovalWithMultipleConnections() {
  console.log("\n🗑️  Testing Edge Removal with Multiple Connections");
  console.log("=" .repeat(60));
  
  // Start with tool_1 having 2 connections
  const toolConnections = {
    tool_1: [
      {
        node_id: "text-component-1",
        node_type: "TextGeneratorComponent",
        node_label: "Text Generator 1",
        component_definition: { name: "TextGeneratorComponent" }
      },
      {
        node_id: "text-component-2", 
        node_type: "TextGeneratorComponent",
        node_label: "Text Generator 2",
        component_definition: { name: "TextGeneratorComponent" }
      }
    ],
    tool_2: [
      {
        node_id: "data-component-1",
        node_type: "DataProcessorComponent", 
        node_label: "Data Processor",
        component_definition: { name: "DataProcessorComponent" }
      }
    ]
  };
  
  console.log("Initial state:");
  console.log(`tool_1: ${toolConnections.tool_1.length} connections`);
  console.log(`tool_2: ${toolConnections.tool_2.length} connections`);
  
  // Simulate removing text-component-1 from tool_1
  const edgeToRemove = {
    source: "text-component-1",
    target: "agentic-ai-1",
    targetHandle: "tool_1"
  };
  
  console.log(`\nRemoving edge: ${edgeToRemove.source} -> ${edgeToRemove.targetHandle}`);
  
  // Simulate the enhanced edge removal logic
  if (toolConnections[edgeToRemove.targetHandle]) {
    if (Array.isArray(toolConnections[edgeToRemove.targetHandle])) {
      // Remove the specific connection from the array
      toolConnections[edgeToRemove.targetHandle] = toolConnections[edgeToRemove.targetHandle].filter(
        (conn) => conn.node_id !== edgeToRemove.source
      );
      
      // If array is empty, remove the handle entirely
      if (toolConnections[edgeToRemove.targetHandle].length === 0) {
        delete toolConnections[edgeToRemove.targetHandle];
      }
    }
  }
  
  console.log("\nAfter removal:");
  console.log(`tool_1: ${toolConnections.tool_1?.length || 0} connections`);
  console.log(`tool_2: ${toolConnections.tool_2?.length || 0} connections`);
  
  // Verify results
  const tool1HasOneConnection = toolConnections.tool_1?.length === 1;
  const tool2Unchanged = toolConnections.tool_2?.length === 1;
  const remainingConnectionCorrect = toolConnections.tool_1?.[0]?.node_id === "text-component-2";
  
  if (tool1HasOneConnection && tool2Unchanged && remainingConnectionCorrect) {
    console.log("✅ Edge removal correctly handled multiple connections");
  } else {
    console.log("❌ Edge removal failed to handle multiple connections correctly");
  }
  
  return {
    success: tool1HasOneConnection && tool2Unchanged && remainingConnectionCorrect,
    finalState: toolConnections
  };
}

// Run all tests
console.log("🚀 Starting Multiple Tools Per Handle Tests\n");

const connectionTest = testMultipleToolsPerHandle();
const removalTest = testEdgeRemovalWithMultipleConnections();

console.log("\n📈 Test Summary:");
console.log("=" .repeat(40));
console.log(`Multiple Connections: ${connectionTest.success ? '✅ PASS' : '❌ FAIL'}`);
console.log(`Edge Removal: ${removalTest.success ? '✅ PASS' : '❌ FAIL'}`);
console.log(`Overall: ${connectionTest.success && removalTest.success ? '✅ ALL TESTS PASS' : '❌ SOME TESTS FAILED'}`);

if (connectionTest.success && removalTest.success) {
  console.log("\n🎉 Multiple tools per handle is now fully supported!");
  console.log("   - Multiple tools can connect to the same handle (e.g., tool_1)");
  console.log("   - Tool connections are stored as arrays");
  console.log("   - Processing logic handles multiple tools correctly");
  console.log("   - Edge removal works properly with multiple connections");
}