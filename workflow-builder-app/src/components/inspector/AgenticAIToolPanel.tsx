/**
 * AgenticAI Tool Panel component for managing tool connections
 */

import React, { useMemo } from "react";
import { Node, Edge } from "reactflow";
import { WorkflowNodeData } from "@/types";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Wrench } from "lucide-react";
import { ToolConnectionCard, ToolConnection } from "./ToolConnectionCard";
import {
  calculateToolConnectionState
} from "@/utils/toolConnectionUtils";

interface AgenticAIToolPanelProps {
  node: Node<WorkflowNodeData>;
  nodes: Node<WorkflowNodeData>[];
  edges: Edge[];
  onConfigChange: (key: string, value: any) => void;
}


/**
 * AgenticAI Tool Panel component
 */
export function AgenticAIToolPanel({
  node,
  nodes,
  edges,
  onConfigChange,
}: AgenticAIToolPanelProps) {
  // Calculate tool connection state
  const toolConnectionState = useMemo(() => {
    return calculateToolConnectionState(node.id, edges, nodes);
  }, [node.id, edges, nodes]);

  // Get current tool handle count (always 1 for single handle approach)
  const currentToolHandleCount = 1;

  const { connectedTools, toolCount, hasToolConnections } = toolConnectionState;

  return (
    <div className="space-y-4" role="region" aria-label="Agent Tools">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Wrench className="h-4 w-4 text-orange-600" />
          <h3 className="text-sm font-medium">🔧 Agent Tools</h3>
          {hasToolConnections && (
            <Badge variant="secondary" className="text-xs">
              {toolCount}
            </Badge>
          )}
        </div>
      </div>

      <Separator />

      {/* Connected Components Section */}
      <div className="space-y-3">
        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
          Connected Components
        </h4>

        {/* Tool Connection Cards */}
        {hasToolConnections ? (
          <div className="space-y-2">
            {connectedTools.map((toolConnection) => (
              <ToolConnectionCard
                key={`${toolConnection.nodeId}-${toolConnection.handleId}`}
                toolConnection={toolConnection}
              />
            ))}
          </div>
        ) : (
          /* Empty State */
          <div className="rounded-lg border-2 border-dashed border-gray-200 p-6 text-center dark:border-gray-700">
            <Wrench className="mx-auto h-8 w-8 text-gray-400" />
            <h4 className="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">
              No tools connected
            </h4>
            <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
              Connect components to tool handles to use them as agent tools
            </p>
          </div>
        )}
      </div>

      {/* Tool Management Info */}
      <div className="rounded-md bg-blue-50 p-3 dark:bg-blue-950">
        <div className="flex">
          <div className="ml-3">
            <h4 className="text-xs font-medium text-blue-800 dark:text-blue-200">
              Tool Management
            </h4>
            <div className="mt-1 text-xs text-blue-700 dark:text-blue-300">
              <ul className="list-disc list-inside space-y-1">
                <li>Connect components to the "Tools" handle to use as agent tools</li>
                <li>Multiple tools can connect to the same handle</li>
                <li>MCP marketplace components are automatically detected</li>
                <li>Tool connections are processed during agent execution</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
