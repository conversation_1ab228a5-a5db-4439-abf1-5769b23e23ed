"""
Universal tool schema generation for AutoGen-compatible tool schemas
"""

import re
import time
from typing import Dict, Any, List, Optional, Union
from app.models.workflow_builder.components import (
    InputBase,
    HandleInput,
    DynamicHandleInput,
    ButtonInput
)


def generate_autogen_tool_schema(component_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Generate AutoGen-compatible tool schema from component data.
    
    Args:
        component_data: Component data dictionary containing inputs and metadata
        
    Returns:
        AutoGen-compatible tool schema dictionary
    """
    return ToolSchemaGenerator().generate_schema(component_data)


def convert_component_to_tool_schema(component_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Convert workflow component to AutoGen tool schema.
    
    Args:
        component_data: Component data dictionary
        
    Returns:
        AutoGen tool schema dictionary
        
    Raises:
        ValueError: If required fields are missing
    """
    # Validate required fields
    required_fields = ["component_type", "component_name"]
    for field in required_fields:
        if field not in component_data:
            raise ValueError(f"Missing required field: {field}")
    
    # Generate tool name from component name (snake_case)
    tool_name = _generate_tool_name(component_data["component_name"])
    
    # Generate description
    description = f"{component_data['component_name']} - {component_data.get('description', 'Workflow component tool')}"
    
    # Process inputs to generate parameters
    inputs = component_data.get("inputs", [])
    filtered_inputs = filter_component_inputs(inputs)
    
    parameters = {
        "type": "object",
        "properties": {},
        "required": [],
        "additionalProperties": False
    }
    
    # Convert each input to parameter
    for input_data in filtered_inputs:
        param_name = input_data.get("name") if isinstance(input_data, dict) else input_data.name
        param_schema = _convert_input_to_parameter(input_data)
        
        if param_schema:
            parameters["properties"][param_name] = param_schema
            
            # Check if required
            is_required = input_data.get("required", False) if isinstance(input_data, dict) else getattr(input_data, "required", False)
            if is_required:
                parameters["required"].append(param_name)
    
    return {
        "name": tool_name,
        "description": description,
        "parameters": parameters,
        "strict": False
    }


def filter_component_inputs(inputs: List[Union[Dict[str, Any], InputBase]]) -> List[Union[Dict[str, Any], InputBase]]:
    """
    Filter component inputs to exclude UI-only elements and handles.
    
    Args:
        inputs: List of input definitions (dict or InputBase objects)
        
    Returns:
        Filtered list of inputs suitable for tool schema generation
    """
    filtered = []
    
    for input_item in inputs:
        # Handle InputBase objects
        if hasattr(input_item, '__class__'):
            # Skip handle inputs and dynamic handle inputs
            if isinstance(input_item, (HandleInput, DynamicHandleInput)):
                continue

            # Skip UI-only inputs (buttons, etc.)
            if isinstance(input_item, ButtonInput):
                continue
        
        # Handle dictionary inputs
        elif isinstance(input_item, dict):
            # Skip handle-type inputs
            input_type = input_item.get("input_type", "")
            if input_type in ["handle", "dynamic_handle", "button"]:
                continue
        
        filtered.append(input_item)
    
    return filtered


def _generate_tool_name(component_name: str) -> str:
    """
    Generate snake_case tool name from component display name.
    
    Args:
        component_name: Human-readable component name
        
    Returns:
        Snake_case tool name
    """
    # Convert to lowercase and replace spaces/special chars with underscores
    tool_name = re.sub(r'[^a-zA-Z0-9]+', '_', component_name.lower())
    
    # Remove leading/trailing underscores
    tool_name = tool_name.strip('_')
    
    # Ensure it starts with a letter
    if tool_name and not tool_name[0].isalpha():
        tool_name = f"tool_{tool_name}"
    
    return tool_name or "unnamed_tool"


def _convert_input_to_parameter(input_data: Union[Dict[str, Any], InputBase]) -> Optional[Dict[str, Any]]:
    """
    Convert input definition to AutoGen parameter schema.
    
    Args:
        input_data: Input definition (dict or InputBase object)
        
    Returns:
        Parameter schema dictionary or None if invalid
    """
    try:
        # Extract input properties
        if isinstance(input_data, dict):
            input_type = input_data.get("input_type", "string")
            display_name = input_data.get("display_name", "")
            description = input_data.get("info", input_data.get("description", ""))
            options = input_data.get("options", [])
        else:
            input_type = getattr(input_data, "input_type", "string")
            display_name = getattr(input_data, "display_name", "")
            description = getattr(input_data, "info", "")
            options = getattr(input_data, "options", [])
        
        # Map input types to JSON schema types
        type_mapping = {
            "string": "string",
            "int": "integer",
            "integer": "integer",
            "bool": "boolean",
            "boolean": "boolean",
            "float": "number",
            "number": "number",
            "dropdown": "string",
            "list": "array",
            "dict": "object",
            "object": "object"
        }

        # Skip unknown input types
        if input_type not in type_mapping:
            return None

        json_type = type_mapping[input_type]
        
        param_schema = {
            "type": json_type,
            "title": display_name,
            "description": description or display_name
        }
        
        # Add enum for dropdown inputs
        if input_type == "dropdown" and options:
            param_schema["enum"] = options
        
        return param_schema
        
    except Exception:
        # Skip invalid inputs gracefully
        return None


class ToolSchemaGenerator:
    """
    Universal tool schema generator for workflow components.
    """
    
    def __init__(self):
        """Initialize the tool schema generator."""
        self.mcp_converter = MCPSchemaConverter()
    
    def generate_schema(self, component_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate AutoGen tool schema from component data.
        
        Args:
            component_data: Component data dictionary
            
        Returns:
            AutoGen-compatible tool schema
        """
        return convert_component_to_tool_schema(component_data)
    
    def generate_universal_schema(self, component_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate tool schema for both regular and MCP components.
        
        Args:
            component_data: Component data dictionary
            
        Returns:
            AutoGen-compatible tool schema
        """
        component_type = component_data.get("component_type", "")
        
        # Handle MCP marketplace components
        if component_type == "MCPMarketplace":
            return self.mcp_converter.convert_mcp_to_autogen_schema(component_data)
        
        # Handle regular components
        return self.generate_schema(component_data)
    
    def generate_batch_schemas(self, components: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Generate tool schemas for multiple components efficiently.
        
        Args:
            components: List of component data dictionaries
            
        Returns:
            List of AutoGen-compatible tool schemas
        """
        schemas = []
        
        for component in components:
            try:
                schema = self.generate_universal_schema(component)
                schemas.append(schema)
            except Exception:
                # Skip invalid components gracefully
                continue
        
        return schemas


class MCPSchemaConverter:
    """
    Converter for MCP marketplace component schemas.
    """
    
    def convert_mcp_to_autogen_schema(self, mcp_component: Dict[str, Any]) -> Dict[str, Any]:
        """
        Convert MCP component to AutoGen tool schema.
        
        Args:
            mcp_component: MCP component data dictionary
            
        Returns:
            AutoGen-compatible tool schema
            
        Raises:
            ValueError: If MCP metadata is missing or invalid
        """
        # Validate MCP metadata
        if "mcp_metadata" not in mcp_component:
            raise ValueError("Missing MCP metadata for MCP component")
        
        mcp_metadata = mcp_component["mcp_metadata"]
        
        if "tool_schema" not in mcp_metadata:
            raise ValueError("Missing tool schema in MCP metadata")
        
        tool_schema = mcp_metadata["tool_schema"]
        
        # Extract schema components
        name = tool_schema.get("name", "unnamed_mcp_tool")
        description = tool_schema.get("description", f"MCP tool: {name}")
        parameters = tool_schema.get("parameters", {
            "type": "object",
            "properties": {},
            "required": [],
            "additionalProperties": False
        })
        
        # Ensure parameters has required structure
        if "type" not in parameters:
            parameters["type"] = "object"
        if "properties" not in parameters:
            parameters["properties"] = {}
        if "required" not in parameters:
            parameters["required"] = []
        if "additionalProperties" not in parameters:
            parameters["additionalProperties"] = False
        
        return {
            "name": name,
            "description": description,
            "parameters": parameters,
            "strict": False
        }
